"""
项目服务层
处理项目相关的业务逻辑
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from fastapi import HTTPException, status

from ..models.project import Project, ProjectStatus
from ..models.user import User, UserRole
from ..schemas.project import ProjectCreate, ProjectUpdate, ProjectQuery
from ..core.rbac import check_user_permission


class ProjectService:
    """项目服务类"""

    @staticmethod
    def create_project(db: Session, project_data: ProjectCreate, current_user: User) -> Project:
        """
        创建新项目
        
        Args:
            db: 数据库会话
            project_data: 项目创建数据
            current_user: 当前用户
        
        Returns:
            创建的项目对象
        """
        # 验证权限：只有经理和管理员可以创建项目
        if not check_user_permission(current_user, [UserRole.MANAGER, UserRole.ADMIN]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有经理和管理员可以创建项目"
            )
        
        # 验证经理ID（如果提供）
        manager_id = project_data.manager_id
        if manager_id:
            manager = db.query(User).filter(User.id == manager_id).first()
            if not manager:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="指定的经理不存在"
                )
            if not check_user_permission(manager, [UserRole.MANAGER, UserRole.ADMIN]):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="指定的用户不是经理或管理员"
                )
        else:
            # 如果没有指定经理，默认设置当前用户为经理（如果是经理角色）
            if check_user_permission(current_user, [UserRole.MANAGER]):
                manager_id = current_user.id

        # 创建项目
        project = Project(
            name=project_data.name,
            description=project_data.description,
            deadline=project_data.deadline,
            created_by_user_id=current_user.id,
            manager_id=manager_id,
            status=ProjectStatus.NEW
        )
        
        db.add(project)
        db.commit()
        db.refresh(project)
        
        return project

    @staticmethod
    def get_project_by_id(db: Session, project_id: int, current_user: User) -> Optional[Project]:
        """
        根据ID获取项目
        
        Args:
            db: 数据库会话
            project_id: 项目ID
            current_user: 当前用户
        
        Returns:
            项目对象或None
        """
        project = db.query(Project).filter(Project.id == project_id).first()
        
        if not project:
            return None
        
        # 检查访问权限
        if not project.can_be_accessed_by(current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="您没有权限访问此项目"
            )
        
        return project

    @staticmethod
    def get_user_projects(db: Session, current_user: User, query: ProjectQuery) -> Dict[str, Any]:
        """
        获取用户可访问的项目列表
        
        Args:
            db: 数据库会话
            current_user: 当前用户
            query: 查询参数
        
        Returns:
            包含项目列表和分页信息的字典
        """
        # 构建基础查询
        base_query = db.query(Project)
        
        # 根据用户角色过滤项目
        if check_user_permission(current_user, [UserRole.ADMIN]):
            # 管理员可以看到所有项目
            pass
        elif check_user_permission(current_user, [UserRole.MANAGER]):
            # 经理可以看到自己管理的项目和自己创建的项目
            base_query = base_query.filter(
                or_(
                    Project.manager_id == current_user.id,
                    Project.created_by_user_id == current_user.id
                )
            )
        else:
            # 专员只能看到分配给自己的项目
            base_query = base_query.filter(Project.created_by_user_id == current_user.id)
        
        # 应用查询过滤器
        if query.name:
            base_query = base_query.filter(Project.name.ilike(f"%{query.name}%"))
        
        if query.status:
            base_query = base_query.filter(Project.status == query.status.value)
        
        if query.created_by_user_id:
            base_query = base_query.filter(Project.created_by_user_id == query.created_by_user_id)
        
        if query.manager_id:
            base_query = base_query.filter(Project.manager_id == query.manager_id)
        
        # 计算总数
        total = base_query.count()
        
        # 应用分页
        offset = (query.page - 1) * query.page_size
        projects = base_query.order_by(Project.created_at.desc()).offset(offset).limit(query.page_size).all()
        
        return {
            "items": projects,
            "pagination": {
                "page": query.page,
                "page_size": query.page_size,
                "total": total,
                "pages": (total + query.page_size - 1) // query.page_size
            }
        }

    @staticmethod
    def update_project(db: Session, project_id: int, project_data: ProjectUpdate, current_user: User) -> Project:
        """
        更新项目
        
        Args:
            db: 数据库会话
            project_id: 项目ID
            project_data: 更新数据
            current_user: 当前用户
        
        Returns:
            更新后的项目对象
        """
        project = db.query(Project).filter(Project.id == project_id).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目不存在"
            )
        
        # 检查编辑权限
        if not project.can_be_edited_by(current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="您没有权限编辑此项目"
            )
        
        # 验证经理ID（如果要更新）
        if project_data.manager_id is not None:
            if project_data.manager_id != project.manager_id:
                manager = db.query(User).filter(User.id == project_data.manager_id).first()
                if not manager:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="指定的经理不存在"
                    )
                if not check_user_permission(manager, [UserRole.MANAGER, UserRole.ADMIN]):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="指定的用户不是经理或管理员"
                    )
        
        # 更新项目字段
        update_data = project_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(project, field, value)
        
        db.commit()
        db.refresh(project)
        
        return project

    @staticmethod
    def delete_project(db: Session, project_id: int, current_user: User) -> bool:
        """
        删除项目
        
        Args:
            db: 数据库会话
            project_id: 项目ID
            current_user: 当前用户
        
        Returns:
            是否删除成功
        """
        project = db.query(Project).filter(Project.id == project_id).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目不存在"
            )
        
        # 检查删除权限
        if not project.can_be_deleted_by(current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以删除项目"
            )
        
        db.delete(project)
        db.commit()
        
        return True

    @staticmethod
    def update_project_status(db: Session, project_id: int, new_status: ProjectStatus, current_user: User) -> Project:
        """
        更新项目状态
        
        Args:
            db: 数据库会话
            project_id: 项目ID
            new_status: 新状态
            current_user: 当前用户
        
        Returns:
            更新后的项目对象
        """
        project = db.query(Project).filter(Project.id == project_id).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目不存在"
            )
        
        # 检查编辑权限
        if not project.can_be_edited_by(current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="您没有权限修改此项目状态"
            )
        
        # 验证状态转换（可以在这里添加状态转换逻辑）
        project.status = new_status.value
        
        db.commit()
        db.refresh(project)
        
        return project

    @staticmethod
    def assign_manager(db: Session, project_id: int, manager_id: int, current_user: User) -> Project:
        """
        分配项目经理
        
        Args:
            db: 数据库会话
            project_id: 项目ID
            manager_id: 经理ID
            current_user: 当前用户
        
        Returns:
            更新后的项目对象
        """
        # 只有管理员可以分配项目经理
        if not check_user_permission(current_user, [UserRole.ADMIN]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以分配项目经理"
            )
        
        project = db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目不存在"
            )
        
        manager = db.query(User).filter(User.id == manager_id).first()
        if not manager:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="指定的经理不存在"
            )
        
        if not check_user_permission(manager, [UserRole.MANAGER, UserRole.ADMIN]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的用户不是经理或管理员"
            )
        
        project.manager_id = manager_id
        db.commit()
        db.refresh(project)
        
        return project

    @staticmethod
    def get_project_statistics(db: Session, current_user: User) -> Dict[str, Any]:
        """
        获取项目统计信息
        
        Args:
            db: 数据库会话
            current_user: 当前用户
        
        Returns:
            统计信息字典
        """
        # 构建基础查询
        base_query = db.query(Project)
        
        # 根据用户角色过滤项目
        if check_user_permission(current_user, [UserRole.ADMIN]):
            # 管理员可以看到所有项目统计
            pass
        elif check_user_permission(current_user, [UserRole.MANAGER]):
            # 经理可以看到自己管理的项目统计
            base_query = base_query.filter(
                or_(
                    Project.manager_id == current_user.id,
                    Project.created_by_user_id == current_user.id
                )
            )
        else:
            # 专员只能看到自己的项目统计
            base_query = base_query.filter(Project.created_by_user_id == current_user.id)
        
        # 统计各状态的项目数量
        total_projects = base_query.count()
        
        status_counts = {}
        for status in ProjectStatus:
            count = base_query.filter(Project.status == status.value).count()
            status_counts[status.value] = count
        
        return {
            "total_projects": total_projects,
            "status_counts": status_counts,
            "user_role": current_user.role
        } 
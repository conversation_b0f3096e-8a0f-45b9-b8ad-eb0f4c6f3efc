import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Dropdown,
  Modal,
  message,
  Progress,
  Row,
  Col,
  Statistic,
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  FilterOutlined,
  ExportOutlined,
  MoreOutlined,
  ProjectOutlined,
  TeamOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { projectAPI, Project, ProjectListResponse, ProjectStatistics, User } from '../../services/api';
import type { ColumnsType } from 'antd/es/table';
import type { MenuProps } from 'antd';
import dayjs from 'dayjs';

const { Search } = Input;
const { Option } = Select;

// 项目状态配置
const PROJECT_STATUS_CONFIG = {
  new: { label: '新建', color: 'default' },
  analyzing: { label: '分析中', color: 'processing' },
  outlining: { label: '大纲生成中', color: 'warning' },
  drafting: { label: '起草中', color: 'cyan' },
  reviewing: { label: '审核中', color: 'orange' },
  completed: { label: '已完成', color: 'success' },
  archived: { label: '已归档', color: 'default' },
};

// 获取项目进度百分比
const getProgressPercentage = (status: string): number => {
  const progressMap: Record<string, number> = {
    new: 0,
    analyzing: 20,
    outlining: 40,
    drafting: 60,
    reviewing: 80,
    completed: 100,
    archived: 100,
  };
  return progressMap[status] || 0;
};

const ProjectListPage: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [statistics, setStatistics] = useState<ProjectStatistics | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    name: '',
    status: '',
    created_by_user_id: undefined as number | undefined,
    manager_id: undefined as number | undefined,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 获取当前用户信息
  const getCurrentUser = (): User | null => {
    const userInfo = localStorage.getItem('user_info');
    return userInfo ? JSON.parse(userInfo) : null;
  };

  const currentUser = getCurrentUser();

  // 获取项目列表
  const fetchProjects = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = {
        page,
        page_size: pageSize,
        ...filters,
      };
      
      const response = await projectAPI.getProjects(params);
      const result: ProjectListResponse = response.data;
      
      if (result.code === 200) {
        setProjects(result.data.items);
        setPagination({
          current: result.data.pagination.page,
          pageSize: result.data.pagination.page_size,
          total: result.data.pagination.total,
        });
      } else {
        message.error(result.message || '获取项目列表失败');
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error('获取项目列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取项目统计
  const fetchStatistics = async () => {
    try {
      const response = await projectAPI.getProjectStatistics();
      const result = response.data;
      
      if (result.code === 200) {
        setStatistics(result.data);
      }
    } catch (error) {
      console.error('获取项目统计失败:', error);
    }
  };

  // 删除项目
  const handleDeleteProject = async (projectId: number) => {
    try {
      const response = await projectAPI.deleteProject(projectId);
      const result = response.data;
      
      if (result.code === 200) {
        message.success('项目删除成功');
        fetchProjects(pagination.current, pagination.pageSize);
        fetchStatistics();
      } else {
        message.error(result.message || '删除项目失败');
      }
    } catch (error) {
      console.error('删除项目失败:', error);
      message.error('删除项目失败');
    }
  };

  // 更新项目状态
  const handleUpdateStatus = async (projectId: number, newStatus: string) => {
    try {
      const response = await projectAPI.updateProjectStatus(projectId, newStatus);
      const result = response.data;
      
      if (result.code === 200) {
        message.success('项目状态更新成功');
        fetchProjects(pagination.current, pagination.pageSize);
        fetchStatistics();
      } else {
        message.error(result.message || '更新项目状态失败');
      }
    } catch (error) {
      console.error('更新项目状态失败:', error);
      message.error('更新项目状态失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<Project> = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string, record: Project) => (
        <div>
          <Button
            type="link"
            onClick={() => navigate(`/projects/${record.id}`)}
            style={{ padding: 0, height: 'auto' }}
          >
            <ProjectOutlined style={{ marginRight: 8 }} />
            {text}
          </Button>
          {record.description && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
              {record.description.length > 50 
                ? `${record.description.substring(0, 50)}...` 
                : record.description}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => {
        const config = PROJECT_STATUS_CONFIG[status as keyof typeof PROJECT_STATUS_CONFIG];
        return (
          <div>
            <Tag color={config?.color || 'default'}>
              {config?.label || status}
            </Tag>
            <Progress
              percent={getProgressPercentage(status)}
              size="small"
              style={{ marginTop: 4 }}
            />
          </div>
        );
      },
    },
    {
      title: '创建者',
      dataIndex: 'created_by_user_id',
      key: 'created_by_user_id',
      width: 100,
      render: (userId: number) => (
        <div>
          <TeamOutlined style={{ marginRight: 4 }} />
          用户{userId}
        </div>
      ),
    },
    {
      title: '项目经理',
      dataIndex: 'manager_id',
      key: 'manager_id',
      width: 100,
      render: (managerId: number | null) => (
        <div>
          {managerId ? (
            <>
              <TeamOutlined style={{ marginRight: 4 }} />
              用户{managerId}
            </>
          ) : (
            <span style={{ color: '#999' }}>未分配</span>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => (
        <div>
          <CalendarOutlined style={{ marginRight: 4 }} />
          {dayjs(date).format('YYYY-MM-DD')}
        </div>
      ),
    },
    {
      title: '截止时间',
      dataIndex: 'deadline',
      key: 'deadline',
      width: 120,
      render: (deadline: string | null) => {
        if (!deadline) return <span style={{ color: '#999' }}>无</span>;
        
        const isOverdue = dayjs().isAfter(dayjs(deadline));
        return (
          <div style={{ color: isOverdue ? '#ff4d4f' : undefined }}>
            <ClockCircleOutlined style={{ marginRight: 4 }} />
            {dayjs(deadline).format('YYYY-MM-DD')}
            {isOverdue && <Tag color="red" style={{ marginLeft: 4 }}>已过期</Tag>}
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record: Project) => {
        const canEdit = currentUser?.role === 'admin' || 
                       (currentUser?.role === 'manager' && 
                        (record.manager_id === currentUser.id || record.created_by_user_id === currentUser.id));
        const canDelete = currentUser?.role === 'admin';

        const statusMenuItems: MenuProps['items'] = Object.entries(PROJECT_STATUS_CONFIG).map(([key, config]) => ({
          key,
          label: config.label,
          disabled: record.status === key,
          onClick: () => handleUpdateStatus(record.id, key),
        }));

        const actionMenuItems: MenuProps['items'] = [
          {
            key: 'view',
            label: '查看详情',
            icon: <EyeOutlined />,
            onClick: () => navigate(`/projects/${record.id}`),
          },
          ...(canEdit ? [{
            key: 'edit',
            label: '编辑项目',
            icon: <EditOutlined />,
            onClick: () => navigate(`/projects/${record.id}/edit`),
          }] : []),
          ...(canEdit ? [{
            key: 'status',
            label: '更新状态',
            icon: <FilterOutlined />,
            children: statusMenuItems,
          }] : []),
          {
            key: 'export',
            label: '导出项目',
            icon: <ExportOutlined />,
            onClick: () => message.info('导出功能开发中'),
          },
          ...(canDelete ? [{
            type: 'divider' as const,
          }, {
            key: 'delete',
            label: '删除项目',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => {
              Modal.confirm({
                title: '确认删除',
                content: `确定要删除项目"${record.name}"吗？此操作不可恢复。`,
                okText: '确认',
                cancelText: '取消',
                okType: 'danger',
                onOk: () => handleDeleteProject(record.id),
              });
            },
          }] : []),
        ];

        return (
          <Space>
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/projects/${record.id}`)}
            >
              查看
            </Button>
            <Dropdown menu={{ items: actionMenuItems }} trigger={['click']}>
              <Button size="small" icon={<MoreOutlined />} />
            </Dropdown>
          </Space>
        );
      },
    },
  ];

  // 搜索处理
  const handleSearch = (value: string) => {
    setFilters({ ...filters, name: value });
  };

  // 状态筛选处理
  const handleStatusFilter = (value: string) => {
    setFilters({ ...filters, status: value });
  };

  // 重置筛选
  const handleResetFilters = () => {
    setFilters({
      name: '',
      status: '',
      created_by_user_id: undefined,
      manager_id: undefined,
    });
  };

  // 批量操作
  const handleBatchAction = (action: string) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要操作的项目');
      return;
    }
    
    switch (action) {
      case 'export':
        message.info('批量导出功能开发中');
        break;
      case 'archive':
        message.info('批量归档功能开发中');
        break;
      default:
        break;
    }
  };

  // 表格选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys),
    getCheckboxProps: (record: Project) => ({
      disabled: false,
      name: record.name,
    }),
  };

  // 初始化数据
  useEffect(() => {
    fetchProjects();
    fetchStatistics();
  }, []);

  // 筛选变化时重新获取数据
  useEffect(() => {
    fetchProjects(1, pagination.pageSize);
  }, [filters]);

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      {statistics && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总项目数"
                value={statistics.total_projects}
                prefix={<ProjectOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="进行中项目"
                value={statistics.status_counts.analyzing + statistics.status_counts.outlining + statistics.status_counts.drafting + statistics.status_counts.reviewing}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已完成项目"
                value={statistics.status_counts.completed || 0}
                prefix={<ProjectOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="我的角色"
                value={statistics.user_role === 'admin' ? '管理员' : statistics.user_role === 'manager' ? '经理' : '专员'}
                prefix={<TeamOutlined />}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 主要内容卡片 */}
      <Card>
        {/* 工具栏 */}
        <div style={{ marginBottom: 16 }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <Search
                  placeholder="搜索项目名称"
                  style={{ width: 200 }}
                  onSearch={handleSearch}
                  allowClear
                />
                <Select
                  placeholder="状态筛选"
                  style={{ width: 120 }}
                  allowClear
                  onChange={handleStatusFilter}
                  value={filters.status || undefined}
                >
                  {Object.entries(PROJECT_STATUS_CONFIG).map(([key, config]) => (
                    <Option key={key} value={key}>
                      {config.label}
                    </Option>
                  ))}
                </Select>
                <Button icon={<ReloadOutlined />} onClick={() => fetchProjects()}>
                  刷新
                </Button>
                <Button onClick={handleResetFilters}>
                  重置筛选
                </Button>
              </Space>
            </Col>
            <Col>
              <Space>
                {selectedRowKeys.length > 0 && (
                  <Space>
                    <Button onClick={() => handleBatchAction('export')}>
                      批量导出
                    </Button>
                    <Button onClick={() => handleBatchAction('archive')}>
                      批量归档
                    </Button>
                  </Space>
                )}
                {(currentUser?.role === 'admin' || currentUser?.role === 'manager') && (
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => navigate('/projects/create')}
                  >
                    创建项目
                  </Button>
                )}
              </Space>
            </Col>
          </Row>
        </div>

        {/* 项目表格 */}
        <Table
          columns={columns}
          dataSource={projects}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              fetchProjects(page, pageSize);
            },
          }}
          rowSelection={rowSelection}
          scroll={{ x: 1000 }}
        />
      </Card>
    </div>
  );
};

export default ProjectListPage; 
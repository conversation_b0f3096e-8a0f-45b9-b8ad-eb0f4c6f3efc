"""
招标文档模型
定义招标文档表的SQLAlchemy模型
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, BigInteger, DateTime, ForeignKey, CheckConstraint, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..database import Base


class ExtractionStatus(str, Enum):
    """文档提取状态枚举"""
    PENDING = "pending"        # 待提取
    PROCESSING = "processing"  # 提取中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 提取失败


class FileType(str, Enum):
    """文件类型枚举"""
    PDF = "pdf"
    DOCX = "docx"
    DOC = "doc"
    TXT = "txt"


class TenderDocument(Base):
    """招标文档模型"""
    __tablename__ = "tender_documents"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), unique=True, nullable=False, index=True)
    original_filename = Column(String(255), nullable=False)
    file_size = Column(BigInteger, nullable=False, default=0)
    file_type = Column(String(50), nullable=False, index=True)
    storage_path = Column(String(1024), nullable=False)
    extracted_text_path = Column(String(1024), nullable=True)
    extraction_status = Column(String(50), nullable=False, default=ExtractionStatus.PENDING.value, index=True)
    page_count = Column(Integer, default=0)
    upload_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    extracted_at = Column(DateTime(timezone=True), nullable=True)

    # 添加约束
    __table_args__ = (
        CheckConstraint(
            extraction_status.in_(['pending', 'processing', 'completed', 'failed']),
            name='check_tender_document_extraction_status'
        ),
        CheckConstraint(
            file_type.in_(['pdf', 'docx', 'doc', 'txt']),
            name='check_tender_document_file_type'
        ),
    )

    # 关系定义
    project = relationship("Project", back_populates="tender_document")
    upload_by = relationship("User", foreign_keys=[upload_by_user_id])
    analysis_reports = relationship("AnalysisReport", back_populates="tender_document")

    def __repr__(self):
        return f"<TenderDocument(id={self.id}, filename='{self.original_filename}', status='{self.extraction_status}')>"

    def dict(self):
        """转换为字典格式，用于API响应"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "original_filename": self.original_filename,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "storage_path": self.storage_path,
            "extracted_text_path": self.extracted_text_path,
            "extraction_status": self.extraction_status,
            "page_count": self.page_count,
            "upload_by_user_id": self.upload_by_user_id,
            "uploaded_at": self.uploaded_at.isoformat() if self.uploaded_at else None,
            "extracted_at": self.extracted_at.isoformat() if self.extracted_at else None,
        }

    def can_be_accessed_by(self, user) -> bool:
        """检查用户是否可以访问此招标文档"""
        from .user import UserRole
        
        # 管理员可以访问所有文档
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 通过项目权限检查
        if hasattr(self, 'project') and self.project:
            return self.project.can_be_accessed_by(user)
        
        return False

    def can_be_edited_by(self, user) -> bool:
        """检查用户是否可以编辑此招标文档"""
        from .user import UserRole
        
        # 管理员可以编辑所有文档
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 通过项目权限检查
        if hasattr(self, 'project') and self.project:
            return self.project.can_be_edited_by(user)
        
        return False

    def can_be_deleted_by(self, user) -> bool:
        """检查用户是否可以删除此招标文档"""
        from .user import UserRole
        
        # 只有管理员可以删除招标文档
        return user.role == UserRole.ADMIN.value

    def is_extraction_completed(self) -> bool:
        """检查文档提取是否完成"""
        return self.extraction_status == ExtractionStatus.COMPLETED.value

    def is_extraction_failed(self) -> bool:
        """检查文档提取是否失败"""
        return self.extraction_status == ExtractionStatus.FAILED.value

    def get_file_size_mb(self) -> float:
        """获取文件大小（MB）"""
        return round(self.file_size / (1024 * 1024), 2) if self.file_size else 0

    def get_file_extension(self) -> str:
        """获取文件扩展名"""
        return self.file_type.lower()

    def update_extraction_status(self, status: ExtractionStatus, extracted_text_path: str = None):
        """更新提取状态"""
        self.extraction_status = status.value
        if status == ExtractionStatus.COMPLETED:
            self.extracted_at = datetime.utcnow()
            if extracted_text_path:
                self.extracted_text_path = extracted_text_path
        elif status == ExtractionStatus.FAILED:
            self.extracted_at = None
            self.extracted_text_path = None 
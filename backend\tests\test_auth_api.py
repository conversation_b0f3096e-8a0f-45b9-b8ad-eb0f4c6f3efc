"""
认证API测试
测试用户注册、登录、令牌验证等功能
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.database import get_db, Base
from app.models.user import UserRole
from app.core.security import get_password_hash
from main import app

# 创建测试数据库
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """测试数据库会话依赖覆盖"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

# 创建测试客户端
client = TestClient(app)


@pytest.fixture(scope="function")
def test_db():
    """测试数据库fixture"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def test_user_data():
    """测试用户数据"""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "role": UserRole.SPECIALIST.value,
        "full_name": "Test User"
    }


def test_register_user(test_db, test_user_data):
    """测试用户注册"""
    response = client.post("/api/v1/auth/register", json=test_user_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["username"] == test_user_data["username"]
    assert data["email"] == test_user_data["email"]
    assert data["role"] == test_user_data["role"]
    assert "id" in data
    assert "hashed_password" not in data  # 密码不应该在响应中


def test_register_duplicate_username(test_db, test_user_data):
    """测试注册重复用户名"""
    # 先注册一个用户
    client.post("/api/v1/auth/register", json=test_user_data)
    
    # 尝试注册相同用户名
    response = client.post("/api/v1/auth/register", json=test_user_data)
    assert response.status_code == 400
    assert "用户名已存在" in response.json()["detail"]


def test_login_success(test_db, test_user_data):
    """测试成功登录"""
    # 先注册用户
    client.post("/api/v1/auth/register", json=test_user_data)
    
    # 登录
    login_data = {
        "username": test_user_data["username"],
        "password": test_user_data["password"]
    }
    response = client.post("/api/v1/auth/login/json", json=login_data)
    assert response.status_code == 200
    
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"
    assert "expires_in" in data


def test_login_with_email(test_db, test_user_data):
    """测试使用邮箱登录"""
    # 先注册用户
    client.post("/api/v1/auth/register", json=test_user_data)
    
    # 使用邮箱登录
    login_data = {
        "username": test_user_data["email"],  # 使用邮箱作为用户名
        "password": test_user_data["password"]
    }
    response = client.post("/api/v1/auth/login/json", json=login_data)
    assert response.status_code == 200


def test_login_wrong_password(test_db, test_user_data):
    """测试错误密码登录"""
    # 先注册用户
    client.post("/api/v1/auth/register", json=test_user_data)
    
    # 使用错误密码登录
    login_data = {
        "username": test_user_data["username"],
        "password": "wrongpassword"
    }
    response = client.post("/api/v1/auth/login/json", json=login_data)
    assert response.status_code == 401
    assert "用户名或密码错误" in response.json()["detail"]


def test_login_nonexistent_user(test_db):
    """测试不存在的用户登录"""
    login_data = {
        "username": "nonexistent",
        "password": "password"
    }
    response = client.post("/api/v1/auth/login/json", json=login_data)
    assert response.status_code == 401


def test_get_current_user(test_db, test_user_data):
    """测试获取当前用户信息"""
    # 注册并登录
    client.post("/api/v1/auth/register", json=test_user_data)
    login_response = client.post("/api/v1/auth/login/json", json={
        "username": test_user_data["username"],
        "password": test_user_data["password"]
    })
    token = login_response.json()["access_token"]
    
    # 获取当前用户信息
    headers = {"Authorization": f"Bearer {token}"}
    response = client.get("/api/v1/auth/me", headers=headers)
    assert response.status_code == 200
    
    data = response.json()
    assert data["username"] == test_user_data["username"]
    assert data["email"] == test_user_data["email"]


def test_get_current_user_invalid_token(test_db):
    """测试无效令牌获取用户信息"""
    headers = {"Authorization": "Bearer invalid_token"}
    response = client.get("/api/v1/auth/me", headers=headers)
    assert response.status_code == 401


def test_oauth2_password_flow(test_db, test_user_data):
    """测试OAuth2密码流程"""
    # 注册用户
    client.post("/api/v1/auth/register", json=test_user_data)
    
    # 使用OAuth2密码流程登录
    form_data = {
        "username": test_user_data["username"],
        "password": test_user_data["password"]
    }
    response = client.post("/api/v1/auth/login", data=form_data)
    assert response.status_code == 200
    
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data


def test_refresh_token(test_db, test_user_data):
    """测试刷新令牌"""
    # 注册并登录
    client.post("/api/v1/auth/register", json=test_user_data)
    login_response = client.post("/api/v1/auth/login/json", json={
        "username": test_user_data["username"],
        "password": test_user_data["password"]
    })
    refresh_token = login_response.json()["refresh_token"]
    
    # 使用刷新令牌获取新的访问令牌
    response = client.post("/api/v1/auth/refresh", json={"refresh_token": refresh_token})
    assert response.status_code == 200
    
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"


def test_logout(test_db, test_user_data):
    """测试登出"""
    # 注册并登录
    client.post("/api/v1/auth/register", json=test_user_data)
    login_response = client.post("/api/v1/auth/login/json", json={
        "username": test_user_data["username"],
        "password": test_user_data["password"]
    })
    token = login_response.json()["access_token"]
    
    # 登出
    headers = {"Authorization": f"Bearer {token}"}
    response = client.post("/api/v1/auth/logout", headers=headers)
    assert response.status_code == 200
    assert "登出成功" in response.json()["message"]


# --- Suggested Test ---
# pytest backend/tests/test_auth_api.py -v 
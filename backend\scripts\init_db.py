#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建数据库表和初始数据
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text
from app.database import engine, create_tables
from app.core.config import settings


def init_database():
    """初始化数据库"""
    print(f"正在连接数据库: {settings.database_url}")
    
    try:
        # 测试数据库连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            print(f"数据库连接成功: {version}")
        
        # 创建所有表
        print("正在创建数据库表...")
        create_tables()
        print("数据库表创建完成")
        
        # 执行初始化SQL脚本
        migration_file = project_root / "migrations" / "001_create_users_table.sql"
        if migration_file.exists():
            print("正在执行数据库迁移脚本...")
            with open(migration_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            with engine.connect() as connection:
                # 分割SQL语句并逐个执行
                statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
                for statement in statements:
                    if statement:
                        try:
                            connection.execute(text(statement))
                            connection.commit()
                        except Exception as e:
                            # 忽略已存在的表或函数等错误
                            if "already exists" not in str(e).lower():
                                print(f"警告: {e}")
            
            print("数据库迁移完成")
        
        print("数据库初始化成功！")
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    init_database() 
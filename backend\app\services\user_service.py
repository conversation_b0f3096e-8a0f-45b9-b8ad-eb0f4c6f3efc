"""
用户服务
处理用户注册、登录、认证等业务逻辑
"""

from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from ..models.user import User, UserRole
from ..schemas.user import UserCreate, UserUpdate
from ..core.security import get_password_hash, verify_password


class UserService:
    """用户服务类"""

    @staticmethod
    def create_user(db: Session, user_create: UserCreate) -> User:
        """
        创建新用户
        
        Args:
            db: 数据库会话
            user_create: 用户创建数据
        
        Returns:
            创建的用户对象
        
        Raises:
            ValueError: 用户名或邮箱已存在
        """
        # 检查用户名是否已存在
        if UserService.get_user_by_username(db, user_create.username):
            raise ValueError("用户名已存在")
        
        # 检查邮箱是否已存在
        if UserService.get_user_by_email(db, user_create.email):
            raise ValueError("邮箱已存在")
        
        # 创建用户对象
        hashed_password = get_password_hash(user_create.password)
        db_user = User(
            username=user_create.username,
            email=user_create.email,
            hashed_password=hashed_password,
            role=user_create.role.value,
            full_name=user_create.full_name,
            is_active=user_create.is_active
        )
        
        try:
            db.add(db_user)
            db.commit()
            db.refresh(db_user)
            return db_user
        except IntegrityError:
            db.rollback()
            raise ValueError("用户创建失败：数据约束冲突")

    @staticmethod
    def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
        """
        验证用户登录
        
        Args:
            db: 数据库会话
            username: 用户名或邮箱
            password: 密码
        
        Returns:
            验证成功返回用户对象，失败返回None
        """
        # 支持用户名或邮箱登录
        user = UserService.get_user_by_username(db, username)
        if not user:
            user = UserService.get_user_by_email(db, username)
        
        if not user:
            return None
        
        if not verify_password(password, user.hashed_password):
            return None
        
        # 检查用户是否激活
        if not user.is_active:
            return None
        
        return user

    @staticmethod
    def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
        """
        根据ID获取用户
        
        Args:
            db: 数据库会话
            user_id: 用户ID
        
        Returns:
            用户对象或None
        """
        return db.query(User).filter(User.id == user_id).first()

    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[User]:
        """
        根据用户名获取用户
        
        Args:
            db: 数据库会话
            username: 用户名
        
        Returns:
            用户对象或None
        """
        return db.query(User).filter(User.username == username).first()

    @staticmethod
    def get_user_by_email(db: Session, email: str) -> Optional[User]:
        """
        根据邮箱获取用户
        
        Args:
            db: 数据库会话
            email: 邮箱
        
        Returns:
            用户对象或None
        """
        return db.query(User).filter(User.email == email).first()

    @staticmethod
    def update_user(db: Session, user_id: int, user_update: UserUpdate) -> Optional[User]:
        """
        更新用户信息
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            user_update: 更新数据
        
        Returns:
            更新后的用户对象或None
        """
        user = UserService.get_user_by_id(db, user_id)
        if not user:
            return None
        
        # 更新字段
        update_data = user_update.dict(exclude_unset=True)
        
        # 处理密码更新
        if "password" in update_data:
            update_data["hashed_password"] = get_password_hash(update_data.pop("password"))
        
        # 处理角色更新
        if "role" in update_data and isinstance(update_data["role"], UserRole):
            update_data["role"] = update_data["role"].value
        
        for field, value in update_data.items():
            setattr(user, field, value)
        
        try:
            db.commit()
            db.refresh(user)
            return user
        except IntegrityError:
            db.rollback()
            raise ValueError("用户更新失败：数据约束冲突")

    @staticmethod
    def deactivate_user(db: Session, user_id: int) -> bool:
        """
        停用用户
        
        Args:
            db: 数据库会话
            user_id: 用户ID
        
        Returns:
            操作是否成功
        """
        user = UserService.get_user_by_id(db, user_id)
        if not user:
            return False
        
        user.is_active = False
        db.commit()
        return True

    @staticmethod
    def get_users(
        db: Session, 
        skip: int = 0, 
        limit: int = 100, 
        role: Optional[UserRole] = None,
        is_active: Optional[bool] = None
    ) -> list[User]:
        """
        获取用户列表
        
        Args:
            db: 数据库会话
            skip: 跳过数量
            limit: 限制数量
            role: 角色过滤
            is_active: 激活状态过滤
        
        Returns:
            用户列表
        """
        query = db.query(User)
        
        if role is not None:
            query = query.filter(User.role == role.value)
        
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        return query.offset(skip).limit(limit).all() 
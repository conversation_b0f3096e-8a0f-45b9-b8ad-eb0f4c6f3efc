{"name": "intellibid-frontend", "version": "1.0.0", "description": "IntelliBid 智能标书生成系统 - 前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.6.43", "ahooks": "^3.7.8", "antd": "^5.12.8", "axios": "^1.6.2", "classnames": "^2.3.2", "dayjs": "^1.11.13", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/ui": "^1.0.4", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "happy-dom": "^12.10.3", "jsdom": "^23.0.1", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
### **智能标-书生成工作流：需求分析与设计规格书 (URS) - V2.0 完整版**

#### **第一章：项目概述 (Project Overview)**

**1.1. 项目愿景 (Vision)** 打造一个由AI驱动的、端到端的智能标书解决方案。该方案能将投标团队从繁琐、重复的文档分析和撰写工作中解放出来，让他们专注于赢标策略、客户关系和方案创新，最终实现**投标响应速度提升80%，标书质量和一致性显著提高，中标率稳步增长**的目标。

**1.2. 核心问题陈述 (Problem Statement)** 当前投标流程面临三大挑战：

- **耗时巨大**: 人工阅读、解析上百页的招标文件通常需要数天时间，效率低下。
- **易于出错**: 资格性、商务、技术等关键要求和评分点容易被遗漏或误解，导致废标或低分。
- **质量不一**: 标书质量高度依赖于项目经理的个人经验和责任心，难以维持统一的高标准。内容复用依赖手动“复制粘贴”，容易出现前后矛盾或信息陈旧的错误。

**1.3. 项目范围 (Scope)**

- **输入 (Input)**: 接收单一的招标文档，支持主流格式（`.docx`, `.pdf`）。
- **核心流程 (Core Workflow)**:
  1. **智能解析**: 自动提取并结构化呈现招标文档的关键信息、要求、评分标准和风险。
  2. **大纲生成**: 基于解析结果，智能生成一份面向得分的、结构清晰的投标书大纲。
  3. **内容生成**: 根据大纲章节，结合内部知识库，自动生成或辅助生成标书初稿。
- **输出 (Output)**:
  - 结构化分析报告（用于决策）。
  - 投标书大纲（用于分工协作）。
  - 可编辑的投标书章节内容及高保真导出的 `.docx` 和 `.pdf` 文件。
- **范围外 (Out of Scope)**:
  - 自动投标/上传。
  - 财务报价的最终决策与计算。
  - 法律条款的最终审核。
  - 图纸（CAD/BIM等）的解析。

#### **第二章：用户与角色分析 (User & Role Analysis)**

| 用户角色                | 核心活动 (Activities)                            | 痛点 (Pain Points)                                           | 对工作流的期望 (Expectations)                                |
| ----------------------- | ------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |
| **投标总监**            | Go/No-Go决策；资源分配；最终审核                 | 快速获取项目核心信息和风险点耗时；难以评估胜算               | 一键生成“决策级”分析报告，快速了解预算、资质、核心要求和潜在风险。 |
| **项目经理/标书经理**   | 全程负责；解析标书；制定大纲；分派任务；内容汇总 | 解析标书细节繁琐，易遗漏；撰写大纲费时费力；反复校对内容一致性。 | 自动化解析与大纲生成，将工作重心转移到策略思考和任务管理。期望有一个强大的编辑器来高效整合内容。 |
| **方案架构师/技术支持** | 撰写技术方案；响应技术参数                       | 需从海量文字中找到所有技术要求和评分点；从知识库查找、改编相似方案。 | 精准定位所有技术得分点；利用编辑器内AI能力（生成、扩写）快速形成技术方案初稿，只需进行优化和深化。 |
| **商务/资质专员**       | 准备资质文件；填写商务条款；处理格式             | 每次投标都要重复准备一套标准文件；确保所有商务条款都被正确响应。 | 自动填充标准商务资质信息；生成商务条款响应自查表，确保100%覆盖。 |

#### **第三章：功能性需求 (Functional Requirements)**

**3.1. 环节一：解析与结构化**

- **F-1.1 文件上传**: 支持用户通过Web界面上传 `.docx` 和 `.pdf` 格式的单一招标文件。
- **F-1.2 文本提取**: 系统后台自动从上传文件中提取全部可读文本，能应对多栏、页眉页脚等复杂格式。
- **F-1.3 结构化分析**: LLM根据预设的 `Prompt_Step1_Analysis`，将提取的文本处理成结构化的分析报告。
- **F-1.4 报告可视化**: 将分析报告以清晰、易读的Web界面呈现给用户。
- **F-1.5 报告导出**: 用户可将分析报告导出为Markdown或PDF文件，用于线下会议和存档。

**3.2. 环节二：大纲与策略生成**

- **F-2.1 触发机制**: 用户在查看分析报告后，可一键触发“生成投标书大纲”功能。
- **F-2.2 大纲生成**: 系统自动调用 `Prompt_Step2_Outline`，将环节一的分析报告作为输入，生成包含三级目录和策略提示的投标书大纲。
- **F-2.3 大纲编辑**: 生成的大纲应是可交互的，允许用户在界面上进行拖拽、修改、删除和添加章节。
- **F-2.4 任务协同（高级功能）**: 允许项目经理将大纲的不同章节指派给不同的团队成员。

**3.3. 环节三：核心内容生成**

- **F-3.1 触发机制**: 用户可点击大纲中的任意章节标题，选择“生成本章内容”。
- **F-3.2 知识库检索 (RAG)**:
  - 系统需内置一个**企业知识库（材料库）**，支持向量化检索。
  - 知识库内容包括：公司简介、各类资质证书说明、标准化解决方案、过往项目案例、核心产品介绍、技术白皮书等。
  - 触发内容生成时，系统以章节标题为查询语句，从知识库中检索最相关的N个内容片段。
- **F-3.3 内容生成**: 系统调用 `Prompt_Step3_ContentGeneration`，将分析报告、大纲、策略提示、以及从知识库检索到的材料一并送入LLM，生成章节初稿。

**3.4. LLM引擎管理与切换**

- **F-3.4.1 模型配置中心 (Admin)**: 提供管理员后台，用于注册和管理LLM，配置信息包括模型名称、类型(`在线API`/`私有化部署`)、API Endpoint、和安全凭证。
- **F-3.4.2 默认模型设置 (Admin)**: 管理员可将任意已配置的模型设置为系统默认模型，初始默认设置为 **DeepSeek**。
- **F-3.4.3 动态模型切换 (User)**: 在关键任务触发点，允许用户从其权限范围内的模型列表中选择本次任务使用的LLM引擎。
- **F-3.4.4 智能路由与兼容性 (Backend)**: 系统后台能根据用户选择，准确路由请求到指定模型，并处理API格式差异。

**3.5. 智能编辑器与文档处理 (基于ProseMirror)**

- **F-3.5.1 结构化文档模型 (Schema Design)**: 必须为投标书设计一个自定义的ProseMirror文档`Schema`，定义如`chapter_title`, `compliance_block`, `scoring_point_block`等结构化节点。
- **F-3.5.2 编辑器用户界面 (UI)**: 提供标准富文本编辑功能，并根据光标所在节点类型，动态显示不同的AI操作选项。
- **F-3.5.3 编辑器内AI能力 (In-Editor AI)**:
  - **内容生成**: 在空的章节下提供“✨ 生成本章内容”按钮，流式生成内容到编辑器。
  - **内容优化**: 用户选中文字后，通过上下文菜单提供“✍️ 优化润色”、“↔️ 扩写内容”、“➡️ 缩写摘要”、“👔 切换风格”等AI辅助功能。
- **F-3.5.4 高保真文档导出**:
  - 提供一键导出为 `.docx` (Word) 和 `.pdf` 文件的功能。
  - 采用**服务端渲染**模式：对PDF使用无头浏览器配合打印CSS；对Word使用专用库（如`python-docx`）程序化构建，确保格式高度保真。

#### **第四章：非功能性需求 (Non-Functional Requirements)**

- **NF-1. 性能 (Performance)**:
  - 文件解析（100页内）应在30秒内完成。
  - 分析报告生成时间应在2分钟内。
  - 章节内容生成与AI优化操作响应时间应在10秒内。
  - 文档导出（100页内）应在1分钟内完成。
  - 性能指标需针对每个已配置的LLM进行独立评估和展示。
- **NF-2. 准确性 (Accuracy)**:
  - 关键信息（预算、截止日期、核心资质）的提取准确率需达到99%以上，并提供用户核对修正界面。
  - 需建立“黄金标准测试集”以评估和对比不同LLM在标书解析任务上的表现。
- **NF-3. 安全性 (Security)**:
  - 所有上传及生成的文档内容需加密存储，并设立严格的角色访问控制。
  - 当选择`私有化部署`模型时，任何项目Prompt数据不得离开企业内网。
  - 当选择`在线API`模型时，需明确提示用户，并可配置数据脱敏规则。
- **NF-4. 可用性 (Usability)**:
  - 界面设计简洁直观，符合投标团队工作习惯。
  - 工作流每一步都有清晰的状态提示。
  - 编辑器需提供稳健的“撤销”和“重做”功能。
- **NF-5. 可扩展性/可维护性 (Maintainability)**:
  - 系统各模块（解析、大纲、生成、导出）需采用微服务或模块化架构，可独立更新。
  - Prompt模板需外部化配置，方便迭代优化。
  - LLM集成层必须采用**适配器模式 (Adapter Pattern)**，确保新增模型只需开发新适配器，不影响核心逻辑。
- **NF-6. 成本管理 (Cost Management)**:
  - 对所有`在线API`模型的调用，需精确记录token用量及费用。
  - 提供管理员成本分析仪表盘，可按日/月、按模型、按用户/项目查看费用。
  - 支持设置月度成本阈值和告警。

#### **第五章：数据与集成需求 (Data & Integration Requirements)**

- **DA-1. 企业知识库**:
  - 需建立统一的知识库管理后台，支持结构化与非结构化文档上传。
  - 文档上传后需自动进行清洗、切块和向量化，以支持RAG。
- **IN-1. LLM引擎服务层**:
  - 构建一个统一的内部调用接口，屏蔽底层模型差异。
  - 为每个支持的LLM（DeepSeek, OpenAI, 私有模型等）实现具体的适配器类。
  - 明确私有化模型部署的软硬件建议配置及API兼容性要求（推荐兼容OpenAI API）。
- **IN-2. 文档处理服务**:
  - 系统需集成独立的、运行在服务器端的文档生成服务，用于处理Word和PDF的高保真导出任务。
- **IN-3. 办公软件集成 (远期)**:
  - 未来可考虑与企业内部OA、CRM或项目管理系统集成，实现任务的自动流转。

#### **第六章：测试与部署规划 (Test & Deployment Plan)**

- **T-1. 测试策略**:
  - **单元测试**: 对各模块（加载器、Prompt链、适配器）进行独立测试。
  - **集成测试**: 测试端到端工作流的数据传递与功能联动。
  - **用户验收测试 (UAT)**: 邀请核心用户使用真实历史标书进行测试，并收集反馈。
- **D-1. 部署策略**:
  - **分阶段上线**:
    - **阶段一 (MVP)**: 上线“解析与结构化”和“大纲生成”功能，让团队快速体验决策效率提升。
    - **阶段二**: 上线“内容生成”及“高级编辑器”功能，赋能创作。
    - **阶段三**: 完善多模型支持和成本管理。
- **D-2. 培训与推广**:
  - 为所有用户提供详细的操作手册和培训课程。
  - 建立反馈渠道，持续收集用户意见，用于工作流和Prompt的迭代优化。
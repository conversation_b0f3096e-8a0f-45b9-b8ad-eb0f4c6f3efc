---
description: 
globs: 
alwaysApply: true
---
# 智能标书生成系统 - Cursor 规则总览

## 项目简介
这是一个由AI驱动的智能标书生成系统，旨在实现**投标响应速度提升80%，标书质量和一致性显著提高**的目标。

## 快速导航

### 📋 项目概述
- [项目概述](mdc:.cursor/rules/project-overview.mdc) - 项目愿景、核心功能和技术栈
- [系统需求规则说明书.md](mdc:docs/系统需求规则说明书.md) - 完整的需求规格书

### 🏗️ 架构设计
- [架构指导原则](mdc:.cursor/rules/architecture-guide.mdc) - 系统架构和设计原则
- [系统架构设计说明书.md](mdc:docs/系统架构设计说明书.md) - 详细的架构设计

### 💻 开发规范
- [开发标准](mdc:.cursor/rules/development-standards.mdc) - 编码规范和开发流程
- [API设计规范](mdc:.cursor/rules/api-design.mdc) - API接口设计和调用规范
- [开发任务清单.md](mdc:docs/开发任务清单.md) - 详细的开发任务和优先级

### 🗄️ 数据设计
- [数据库模式](mdc:.cursor/rules/database-schema.mdc) - 核心数据模型和表结构
- [系统详细设计说明书.md](mdc:docs/系统详细设计说明书.md) - 详细的系统设计

### 🤖 AI集成
- [LLM集成指南](mdc:.cursor/rules/llm-integration.mdc) - 多LLM支持和适配器模式

### 📝 前端开发
- [前端编辑器开发](mdc:.cursor/rules/frontend-editor.mdc) - ProseMirror编辑器和AI集成

### 📊 任务管理
- [任务跟踪规则](mdc:.cursor/rules/task-tracking.mdc) - 任务状态跟踪和更新规则（使用真实系统时间）
- [任务与更新说明.md](mdc:任务与更新说明.md) - 所有任务状态和更新记录

## 核心工作流程

### 1. 智能解析
用户上传招标文档 → 文档解析引擎提取文本 → LLM分析生成结构化报告

### 2. 大纲生成
基于分析报告 → LLM生成投标书大纲 → 用户可编辑和调整

### 3. 内容生成
选择章节 → RAG知识库检索 → LLM生成章节内容 → ProseMirror编辑器展示

### 4. 智能优化
用户选中文本 → AI优化操作(润色/扩写/摘要) → 实时更新编辑器

### 5. 文档导出
ProseMirror文档JSON → 后端导出服务 → 生成PDF/DOCX文件

## 关键技术要点

### 分层架构
- **前端应用层**: React + ProseMirror编辑器
- **应用服务层**: FastAPI + LangChain工作流
- **核心引擎层**: 文档解析、LLM服务、导出服务
- **数据存储层**: PostgreSQL + pgvector + MinIO

### LLM适配器模式
```python
[前端/工作流] -> [LLM网关] -> [模型适配器] -> [具体LLM API]
```

### 数据安全
- JWT认证 + RBAC权限控制
- 敏感数据加密存储
- 私有化部署支持

## 开发优先级
参考 [开发任务清单.md](mdc:docs/开发任务清单.md)：
- **P0**: 关键路径任务
- **P1**: MVP核心功能
- **P2**: 重要功能
- **P3**: 优化项

## 性能要求
- 文件解析(100页内)：30秒内完成
- 分析报告生成：2分钟内完成
- 章节内容生成：10秒内完成
- 文档导出(100页内)：1分钟内完成

## 用户角色
- **投标总监**: 决策和审核
- **项目经理**: 全程管理和协调
- **技术专家**: 技术方案撰写
- **商务专员**: 商务条款处理

---

**开发提示**: 在开发过程中，请始终参考相关的规则文件和文档，确保代码符合项目的架构设计和开发标准。





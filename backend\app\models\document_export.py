"""
文档导出模型
定义文档导出记录表的SQLAlchemy模型
"""

from datetime import datetime, timedelta
from enum import Enum
from sqlalchemy import Column, Integer, String, BigInteger, DateTime, ForeignKey, CheckConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..database import Base


class ExportStatus(str, Enum):
    """导出状态枚举"""
    PENDING = "pending"        # 待导出
    PROCESSING = "processing"  # 导出中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 导出失败
    EXPIRED = "expired"       # 已过期


class ExportFormat(str, Enum):
    """导出格式枚举"""
    PDF = "pdf"
    DOCX = "docx"
    HTML = "html"


class DocumentExport(Base):
    """文档导出记录模型"""
    __tablename__ = "document_exports"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, index=True)
    bid_document_id = Column(Integer, ForeignKey("bid_documents.id"), nullable=False, index=True)
    export_format = Column(String(20), nullable=False)
    export_status = Column(String(50), nullable=False, default=ExportStatus.PENDING.value, index=True)
    file_path = Column(String(1024), nullable=True)
    file_size = Column(BigInteger, default=0)
    export_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    exported_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)

    # 添加约束
    __table_args__ = (
        CheckConstraint(
            export_status.in_(['pending', 'processing', 'completed', 'failed', 'expired']),
            name='check_document_export_status'
        ),
        CheckConstraint(
            export_format.in_(['pdf', 'docx', 'html']),
            name='check_document_export_format'
        ),
    )

    # 关系定义
    project = relationship("Project")
    bid_document = relationship("BidDocument", back_populates="exports")
    export_by = relationship("User", foreign_keys=[export_by_user_id])

    def __repr__(self):
        return f"<DocumentExport(id={self.id}, format='{self.export_format}', status='{self.export_status}')>"

    def dict(self):
        """转换为字典格式，用于API响应"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "bid_document_id": self.bid_document_id,
            "export_format": self.export_format,
            "export_status": self.export_status,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "export_by_user_id": self.export_by_user_id,
            "exported_at": self.exported_at.isoformat() if self.exported_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
        }

    def can_be_accessed_by(self, user) -> bool:
        """检查用户是否可以访问此导出记录"""
        from .user import UserRole
        
        # 管理员可以访问所有导出记录
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 通过项目权限检查
        if hasattr(self, 'project') and self.project:
            return self.project.can_be_accessed_by(user)
        
        return False

    def can_be_downloaded_by(self, user) -> bool:
        """检查用户是否可以下载此导出文件"""
        # 首先检查访问权限
        if not self.can_be_accessed_by(user):
            return False
        
        # 检查导出状态
        if self.export_status != ExportStatus.COMPLETED.value:
            return False
        
        # 检查是否过期
        if self.is_expired():
            return False
        
        return True

    def is_completed(self) -> bool:
        """检查导出是否完成"""
        return self.export_status == ExportStatus.COMPLETED.value

    def is_failed(self) -> bool:
        """检查导出是否失败"""
        return self.export_status == ExportStatus.FAILED.value

    def is_expired(self) -> bool:
        """检查导出文件是否过期"""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False

    def get_file_size_mb(self) -> float:
        """获取文件大小（MB）"""
        return round(self.file_size / (1024 * 1024), 2) if self.file_size else 0

    def get_download_filename(self) -> str:
        """获取下载文件名"""
        if hasattr(self, 'bid_document') and self.bid_document:
            base_name = self.bid_document.document_title
        else:
            base_name = f"project_{self.project_id}_document"
        
        # 清理文件名中的特殊字符
        safe_name = "".join(c for c in base_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        return f"{safe_name}.{self.export_format}"

    def set_expiration(self, hours: int = 24):
        """设置过期时间"""
        self.expires_at = datetime.utcnow() + timedelta(hours=hours)

    def update_status(self, status: ExportStatus, file_path: str = None, file_size: int = None):
        """更新导出状态"""
        self.export_status = status.value
        
        if status == ExportStatus.COMPLETED:
            if file_path:
                self.file_path = file_path
            if file_size:
                self.file_size = file_size
            # 设置默认过期时间为24小时
            if not self.expires_at:
                self.set_expiration(24)
        elif status == ExportStatus.FAILED:
            self.file_path = None
            self.file_size = 0

    def mark_as_expired(self):
        """标记为过期"""
        self.export_status = ExportStatus.EXPIRED.value

    def get_relative_path(self) -> str:
        """获取相对路径（用于内部存储）"""
        if self.file_path:
            # 移除绝对路径前缀，返回相对路径
            return self.file_path.replace('/uploads/', '')
        return ""

    def get_download_url(self, base_url: str = "") -> str:
        """获取下载URL"""
        if self.is_completed() and not self.is_expired():
            return f"{base_url}/api/v1/exports/{self.id}/download"
        return ""

    def get_time_remaining(self) -> str:
        """获取剩余时间（人类可读格式）"""
        if not self.expires_at:
            return "永不过期"
        
        if self.is_expired():
            return "已过期"
        
        remaining = self.expires_at - datetime.utcnow()
        hours = remaining.seconds // 3600
        minutes = (remaining.seconds % 3600) // 60
        
        if remaining.days > 0:
            return f"{remaining.days}天{hours}小时"
        elif hours > 0:
            return f"{hours}小时{minutes}分钟"
        else:
            return f"{minutes}分钟" 
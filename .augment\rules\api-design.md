---
type: "always_apply"
---
# API设计规范

## 核心API接口设计

### 工作流编排服务API
基于 [系统详细设计说明书.md](docs/系统详细设计说明书.md) 中的API设计：

#### 项目管理
- `POST /api/v1/projects` - 创建新的投标项目
- `GET /api/v1/projects` - 获取项目列表
- `GET /api/v1/projects/{projectId}` - 获取项目详情
- `PUT /api/v1/projects/{projectId}` - 更新项目信息

#### 文档处理
- `POST /api/v1/projects/{projectId}/tender-document` - 上传招标文档并触发解析
- `GET /api/v1/projects/{projectId}/analysis-report` - 获取结构化分析报告
- `POST /api/v1/projects/{projectId}/generate-outline` - 生成投标书大纲
- `PUT /api/v1/projects/{projectId}/bid-document` - 保存/更新投标书内容

#### AI内容生成
- `POST /api/v1/projects/{projectId}/generate-chapter` - 为指定章节生成内容
  ```json
  {
    "chapterId": "3.5.1",
    "llmModelId": "deepseek-v2"
  }
  ```

- `POST /api/v1/ai/optimize-text` - 执行编辑器内AI优化
  ```json
  {
    "text": "...",
    "action": "polish" | "expand" | "summarize",
    "llmModelId": "gpt-4o"
  }
  ```

### 内部服务API

#### LLM服务层
- `POST /internal/v1/llm/chat/completions` - 统一的聊天补全接口
  ```json
  {
    "model": "llmModelId",
    "messages": [{"role": "user", "content": "..."}],
    "stream": false
  }
  ```

#### 知识库服务
- `POST /internal/v1/kb/ingest` - 触发文档入库流程
- `POST /internal/v1/kb/search` - 执行向量相似度搜索
  ```json
  {
    "queryText": "...",
    "topK": 5,
    "filters": {"project_type": "hospital"}
  }
  ```

#### 文档导出服务
- `POST /internal/v1/export` - 执行导出任务
  ```json
  {
    "proseMirrorJson": {...},
    "format": "docx" | "pdf"
  }
  ```

## 响应格式标准

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "details": {
    "field": "具体错误信息"
  }
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 100
    }
  }
}
```

## 认证与授权

### JWT认证
- 所有面向前端的API使用JWT认证
- Token包含用户ID、角色信息和过期时间
- 刷新Token机制确保用户体验

### 基于角色的访问控制(RBAC)
- **admin**: 系统管理员，拥有所有权限
- **manager**: 项目经理，可管理项目和分配任务
- **specialist**: 专员，只能访问分配给自己的任务

### 权限检查中间件
```python
@require_role("admin", "manager")
async def create_project(request: Request):
    # 需要管理员或经理权限
    pass
```

## 错误处理

### 标准错误码
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

### 业务错误码
- `10001` - 文档解析失败
- `10002` - LLM调用失败
- `10003` - 知识库检索失败
- `10004` - 文档导出失败
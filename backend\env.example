# IntelliBid 环境配置示例
# 复制此文件为 .env 并修改相应的值

# 基础配置
APP_NAME=IntelliBid API
APP_VERSION=1.0.0
DEBUG=true

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=*******************************************************************/postgres
DATABASE_ECHO=false

# 认证配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000","http://localhost:5173"]

# 对象存储配置 (MinIO)
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=intellibid

# LLM配置
DEFAULT_LLM_MODEL=deepseek-v2
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_API_ENDPOINT=https://api.deepseek.com/v1

# 日志配置
LOG_LEVEL=INFO 
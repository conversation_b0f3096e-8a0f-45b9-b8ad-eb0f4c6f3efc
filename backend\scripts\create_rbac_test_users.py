#!/usr/bin/env python3
"""
创建RBAC测试用户脚本
为每种角色(<PERSON><PERSON>, Manager, Specialist)创建测试用户
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.database import get_db, engine
from app.models.user import User, UserRole
from app.core.security import get_password_hash

def create_test_users():
    """创建RBAC测试用户"""
    print("="*60)
    print("创建RBAC测试用户")
    print("="*60)
    
    # 创建数据库会话
    db = next(get_db())
    
    # 测试用户数据
    test_users = [
        {
            "username": "admin",
            "email": "<EMAIL>",
            "password": "admin123",
            "role": UserRole.ADMIN,
            "full_name": "系统管理员",
            "description": "系统管理员，拥有所有权限"
        },
        {
            "username": "manager",
            "email": "<EMAIL>", 
            "password": "manager123",
            "role": UserRole.MANAGER,
            "full_name": "项目经理",
            "description": "项目经理，可管理项目和分配任务"
        },
        {
            "username": "specialist",
            "email": "<EMAIL>",
            "password": "specialist123", 
            "role": UserRole.SPECIALIST,
            "full_name": "技术专员",
            "description": "技术专员，只能访问分配的任务"
        }
    ]
    
    created_users = []
    
    for user_data in test_users:
        try:
            # 检查用户是否已存在
            existing_user = db.query(User).filter(
                (User.username == user_data["username"]) | 
                (User.email == user_data["email"])
            ).first()
            
            if existing_user:
                print(f"⚠️  用户 {user_data['username']} 已存在，跳过创建")
                continue
            
            # 创建新用户
            new_user = User(
                username=user_data["username"],
                email=user_data["email"],
                hashed_password=get_password_hash(user_data["password"]),
                role=user_data["role"].value,
                full_name=user_data["full_name"],
                is_active=True
            )
            
            db.add(new_user)
            db.commit()
            db.refresh(new_user)
            
            created_users.append({
                "id": new_user.id,
                "username": new_user.username,
                "email": new_user.email,
                "role": new_user.role,
                "full_name": new_user.full_name,
                "password": user_data["password"],
                "description": user_data["description"]
            })
            
            print(f"✅ 成功创建用户: {user_data['username']} ({user_data['role'].value})")
            
        except Exception as e:
            print(f"❌ 创建用户 {user_data['username']} 失败: {str(e)}")
            db.rollback()
    
    db.close()
    
    # 显示创建结果
    if created_users:
        print(f"\n🎉 成功创建 {len(created_users)} 个测试用户:")
        print("-" * 80)
        print(f"{'用户名':<12} {'角色':<12} {'邮箱':<25} {'密码':<12} {'描述'}")
        print("-" * 80)
        
        for user in created_users:
            print(f"{user['username']:<12} {user['role']:<12} {user['email']:<25} {user['password']:<12} {user['description']}")
        
        print("-" * 80)
        print("\n📝 测试用户登录信息:")
        for user in created_users:
            print(f"  {user['role'].upper()}: {user['username']} / {user['password']}")
    else:
        print("\n📝 没有创建新用户，可能用户已存在")
    
    # 验证用户创建
    print(f"\n🔍 验证数据库中的用户:")
    db = next(get_db())
    all_users = db.query(User).all()
    print(f"数据库中共有 {len(all_users)} 个用户:")
    for user in all_users:
        print(f"  - {user.username} ({user.role}) - {user.email}")
    db.close()

def test_user_login():
    """测试用户登录功能"""
    print(f"\n🧪 测试用户登录功能:")
    
    from app.services.user_service import UserService
    from app.core.security import verify_password
    
    db = next(get_db())
    
    test_credentials = [
        ("admin", "admin123"),
        ("manager", "manager123"),
        ("specialist", "specialist123")
    ]
    
    for username, password in test_credentials:
        user = UserService.get_user_by_username(db, username)
        if user:
            if verify_password(password, user.hashed_password):
                print(f"  ✅ {username} 登录测试通过")
            else:
                print(f"  ❌ {username} 密码验证失败")
        else:
            print(f"  ❌ {username} 用户不存在")
    
    db.close()

def test_rbac_permissions():
    """测试RBAC权限"""
    print(f"\n🔐 测试RBAC权限:")
    
    from app.core.rbac import check_user_permission
    
    db = next(get_db())
    
    # 获取测试用户
    admin_user = db.query(User).filter(User.username == "admin").first()
    manager_user = db.query(User).filter(User.username == "manager").first()
    specialist_user = db.query(User).filter(User.username == "specialist").first()
    
    if not all([admin_user, manager_user, specialist_user]):
        print("  ❌ 测试用户不完整，请先创建测试用户")
        db.close()
        return
    
    # 权限测试用例
    test_cases = [
        (admin_user, [UserRole.ADMIN], "Admin访问Admin权限"),
        (admin_user, [UserRole.MANAGER], "Admin访问Manager权限"),
        (admin_user, [UserRole.SPECIALIST], "Admin访问Specialist权限"),
        (manager_user, [UserRole.ADMIN], "Manager访问Admin权限"),
        (manager_user, [UserRole.MANAGER], "Manager访问Manager权限"),
        (manager_user, [UserRole.SPECIALIST], "Manager访问Specialist权限"),
        (specialist_user, [UserRole.ADMIN], "Specialist访问Admin权限"),
        (specialist_user, [UserRole.MANAGER], "Specialist访问Manager权限"),
        (specialist_user, [UserRole.SPECIALIST], "Specialist访问Specialist权限"),
    ]
    
    for user, required_roles, description in test_cases:
        has_permission = check_user_permission(user, required_roles)
        status = "✅" if has_permission else "❌"
        print(f"  {status} {description}: {has_permission}")
    
    db.close()

def main():
    """主函数"""
    print("RBAC测试用户创建脚本")
    print("此脚本将为每种角色创建一个测试用户")
    
    try:
        # 创建测试用户
        create_test_users()
        
        # 测试用户登录
        test_user_login()
        
        # 测试RBAC权限
        test_rbac_permissions()
        
        print(f"\n🎯 完成！现在可以使用以下用户进行测试:")
        print("  - admin/admin123 (管理员)")
        print("  - manager/manager123 (经理)")
        print("  - specialist/specialist123 (专员)")
        
        print(f"\n📚 测试建议:")
        print("  1. 使用这些用户测试登录API")
        print("  2. 使用不同角色测试RBAC权限端点")
        print("  3. 运行: python scripts/test_rbac_functionality.py")
        
    except Exception as e:
        print(f"❌ 脚本执行失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 
---
type: "always_apply"
---
# 前端编辑器开发指南

## ProseMirror编辑器集成

### 核心架构
基于ProseMirror构建富文本编辑器，支持AI辅助写作和实时协作。

### 编辑器初始化
```javascript
import { EditorState } from "prosemirror-state"
import { EditorView } from "prosemirror-view"
import { Schema } from "prosemirror-model"
import { baseKeymap } from "prosemirror-commands"
import { keymap } from "prosemirror-keymap"

const schema = new Schema({
  nodes: {
    doc: { content: "block+" },
    paragraph: { content: "inline*", group: "block" },
    text: { group: "inline" }
  },
  marks: {
    strong: {},
    em: {}
  }
})

const state = EditorState.create({
  schema,
  plugins: [
    keymap(baseKeymap),
    aiAssistPlugin,
    autoSavePlugin
  ]
})

const view = new EditorView(document.querySelector("#editor"), { state })
```

## AI集成功能

### 文本选择AI操作
```javascript
const aiOperations = {
  polish: "润色优化",
  expand: "扩写内容", 
  summarize: "内容摘要",
  translate: "翻译"
}

function handleAIOperation(operation, selectedText) {
  return fetch('/api/ai/text-operation', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      operation,
      text: selectedText,
      context: getDocumentContext()
    })
  })
}
```

### 章节内容生成
```javascript
async function generateChapterContent(chapterId, requirements) {
  const response = await fetch('/api/ai/generate-content', {
    method: 'POST',
    body: JSON.stringify({
      chapter_id: chapterId,
      requirements,
      project_context: getCurrentProject()
    })
  })
  
  const { content } = await response.json()
  insertContentAtCursor(content)
}
```

## 文档结构管理

### 大纲同步
```javascript
const outlinePlugin = new Plugin({
  key: new PluginKey('outline'),
  
  state: {
    init() {
      return { outline: [] }
    },
    
    apply(tr, value) {
      if (tr.docChanged) {
        return { outline: extractOutline(tr.doc) }
      }
      return value
    }
  }
})

function extractOutline(doc) {
  const outline = []
  doc.descendants((node, pos) => {
    if (node.type.name === 'heading') {
      outline.push({
        level: node.attrs.level,
        text: node.textContent,
        pos
      })
    }
  })
  return outline
}
```

### 导出格式支持
- **PDF**: 使用无头浏览器 + 打印CSS
- **DOCX**: 使用python-docx程序化构建
- **HTML**: 直接从ProseMirror输出

## 用户体验优化

### 自动保存
```javascript
const autoSavePlugin = new Plugin({
  key: new PluginKey('auto-save'),
  
  state: {
    init() {
      return { lastSaved: Date.now() }
    },
    
    apply(tr, value) {
      if (tr.docChanged) {
        return { ...value, needsSave: true }
      }
      return value
    }
  },
  
  view(editorView) {
    const saveInterval = setInterval(() => {
      const state = this.getState(editorView.state)
      if (state.needsSave) {
        saveBidDocument(editorView.state.doc.toJSON())
      }
    }, 30000) // 每30秒自动保存
    
    return {
      destroy() {
        clearInterval(saveInterval)
      }
    }
  }
})
```
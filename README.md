# IntelliBid - 智能标书生成系统

🚀 **AI驱动的投标文档解决方案，实现投标响应速度提升80%，标书质量和一致性显著提高**

## 🎯 项目愿景

打造一个端到端的智能标书解决方案，通过AI技术自动化处理招标文档解析、分析报告生成和投标书内容创作，帮助企业提高中标率。

## ✨ 核心功能

### 🔍 智能解析
- 自动提取招标文档关键信息
- 结构化呈现要求、评分标准和风险点
- 支持PDF、DOCX等多种格式

### 📊 AI分析
- 基于LLM的深度文档分析
- 生成结构化分析报告
- 识别关键得分点和合规要求

### 📝 内容生成
- 智能生成投标书大纲
- 基于知识库的章节内容生成
- ProseMirror编辑器集成AI优化功能

## 🏗️ 技术架构

### 后端技术栈
- **框架**: FastAPI 0.104.1
- **数据库**: PostgreSQL + pgvector
- **存储**: MinIO对象存储
- **AI**: 多LLM支持(DeepSeek, OpenAI等)
- **部署**: Docker + Kubernetes

### 前端技术栈
- **框架**: React + Vite
- **编辑器**: ProseMirror
- **UI组件**: Ant Design Pro
- **状态管理**: Redux Toolkit

## 🚀 快速开始

### 环境要求
- Python 3.10+
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd IntelliBid
```

2. **配置环境变量**
```bash
cp backend/env.example backend/.env
# 编辑 backend/.env 文件，配置数据库和API密钥
```

3. **启动开发环境**
```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 或者分别启动后端和前端
cd backend
pip install -r requirements.txt
uvicorn main:app --reload

cd frontend
npm install
npm run dev
```

4. **访问应用**
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs
- 前端应用: http://localhost:3000

## 📖 API文档

### 核心端点

#### 健康检查
```http
GET /health
```

#### 项目管理
```http
POST /api/v1/projects
GET /api/v1/projects
GET /api/v1/projects/{projectId}
PUT /api/v1/projects/{projectId}
```

#### 文档处理
```http
POST /api/v1/projects/{projectId}/tender-document
GET /api/v1/projects/{projectId}/analysis-report
POST /api/v1/projects/{projectId}/generate-outline
```

详细API文档请访问: http://localhost:8000/docs

## 🏗️ 项目结构

```
IntelliBid/
├── backend/                 # 后端服务
│   ├── app/                # 应用核心代码
│   │   ├── core/          # 核心配置和工具
│   │   ├── api/           # API路由
│   │   ├── models/        # 数据模型
│   │   └── services/      # 业务服务
│   ├── tests/             # 测试文件
│   ├── main.py            # 应用入口
│   └── requirements.txt   # Python依赖
├── frontend/              # 前端应用
├── docs/                  # 项目文档
├── .github/workflows/     # CI/CD流水线
├── docker-compose.yml     # 开发环境配置
└── Dockerfile            # 容器构建文件
```

## 🧪 测试

### 运行后端测试
```bash
cd backend
pytest --cov=app --cov-report=html
```

### 代码质量检查
```bash
cd backend
black .                    # 代码格式化
isort .                    # 导入排序
flake8 .                   # 代码风格检查
mypy .                     # 类型检查
```

## 🚀 部署

### Docker部署
```bash
# 构建生产镜像
docker build --target production -t intellibid:latest .

# 运行容器
docker run -p 8000:8000 intellibid:latest
```

### Kubernetes部署
```bash
# 应用Kubernetes配置
kubectl apply -f k8s/
```

## 📊 开发进度

- [x] **CORE-101**: FastAPI项目初始化 ✅
- [ ] **CORE-102**: React前端项目初始化
- [ ] **CORE-103**: 数据库表结构设计
- [ ] **CORE-104**: 用户认证API开发

详细任务进度请查看: [任务与更新说明.md](./任务与更新说明.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙋‍♂️ 支持

如有问题或建议，请通过以下方式联系：

- 📧 Email: <EMAIL>
- 💬 Issue: [GitHub Issues](https://github.com/your-org/intellibid/issues)
- 📖 文档: [项目文档](./docs/)

---

**IntelliBid Team** - 让AI为投标赋能 🚀 
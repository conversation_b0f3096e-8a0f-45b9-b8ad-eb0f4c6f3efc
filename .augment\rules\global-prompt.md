---
type: "always_apply"
---
# 全局提示与开发指导

## 项目上下文
你正在开发一个**智能标书生成系统**，这是一个由AI驱动的企业级投标文档创作平台。

## 核心目标
- **投标响应速度提升80%**
- **标书质量和一致性显著提高**
- **中标率稳步增长**

## 关键技术特点
- **多LLM支持**: DeepSeek、OpenAI、私有化模型
- **适配器模式**: 统一的LLM调用接口
- **ProseMirror编辑器**: 富文本编辑与AI集成
- **RAG知识库**: 企业知识检索增强
- **RBAC权限控制**: 严格的角色访问控制

## 开发原则

### 1. 安全优先
- **所有API端点必须实现RBAC权限控制**
- 敏感数据加密存储
- JWT认证 + 角色授权

### 2. 性能要求
- 文件解析(100页): ≤30秒
- 分析报告生成: ≤2分钟  
- AI内容生成: ≤10秒
- 文档导出(100页): ≤1分钟

### 3. 代码质量
- 完整的类型注解
- 异步编程模式
- 统一的错误处理
- 测试覆盖率≥80%

## 架构层次
```
前端应用层 (React + ProseMirror)
    ↓
应用服务层 (FastAPI + LangChain)  
    ↓
核心引擎层 (LLM服务 + 文档处理)
    ↓
数据存储层 (PostgreSQL + pgvector + MinIO)
```

## 开发检查清单

### API开发
- [ ] 导入RBAC权限模块
- [ ] 添加权限检查依赖
- [ ] 实现统一错误处理
- [ ] 编写完整的类型注解
- [ ] 添加API文档说明
- [ ] 编写测试用例

### 前端开发
- [ ] TypeScript严格类型检查
- [ ] 组件Props接口定义
- [ ] 错误边界处理
- [ ] 加载状态管理
- [ ] 响应式设计

### 数据库操作
- [ ] 使用参数化查询
- [ ] 添加适当索引
- [ ] 外键约束检查
- [ ] 事务处理
- [ ] 数据验证

## 常用代码模板

### FastAPI路由模板
```python
from fastapi import APIRouter, Depends, HTTPException
from app.core.rbac import require_manager
from app.models.user import User

router = APIRouter()

@router.post("/endpoint")
async def create_resource(
    data: CreateResourceRequest,
    current_user: User = Depends(require_manager())
):
    try:
        # 业务逻辑
        result = await service.create(data)
        return {"code": 200, "message": "success", "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### React组件模板
```typescript
import React, { useState, useEffect } from 'react';
import { Button, message } from 'antd';

interface ComponentProps {
  id: number;
  onSuccess?: () => void;
}

const Component: React.FC<ComponentProps> = ({ id, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  
  const handleAction = async () => {
    try {
      setLoading(true);
      await api.action(id);
      message.success('操作成功');
      onSuccess?.();
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button loading={loading} onClick={handleAction}>
      执行操作
    </Button>
  );
};

export default Component;
```

## 错误处理标准
```python
# 统一响应格式
{
  "code": 200,
  "message": "success", 
  "data": {...}
}

# 错误响应格式
{
  "code": 400,
  "message": "错误描述",
  "details": {...}
}
```

## 关键文档参考
- [系统需求规则说明书.md](docs/系统需求规则说明书.md)
- [系统架构设计说明书.md](docs/系统架构设计说明书.md)
- [开发任务清单.md](docs/开发任务清单.md)
- [RBAC开发指导原则](.augment/rules/rbac-development-guidelines.md)
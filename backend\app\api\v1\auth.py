"""
认证相关API路由
包括用户登录、注册、令牌刷新等功能
"""

from datetime import timedelta
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from ...database import get_db
from ...core.security import create_access_token, create_refresh_token, verify_refresh_token
from ...core.config import settings
from ...services.user_service import UserService
from ...schemas.user import UserLogin, Token, UserCreate, UserResponse, LoginResponse
from ...core.deps import get_current_user
from ...models.user import User

router = APIRouter()


@router.post("/login", response_model=Token, summary="用户登录")
async def login(
    db: Session = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    用户登录
    
    - **username**: 用户名或邮箱
    - **password**: 密码
    
    返回访问令牌和刷新令牌
    """
    user = UserService.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        subject=user.username, expires_delta=access_token_expires
    )
    
    # 创建刷新令牌
    refresh_token = create_refresh_token(subject=user.username)
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.access_token_expire_minutes * 60,
        "refresh_token": refresh_token
    }


@router.options("/login/json", summary="登录预检请求")
async def login_json_options():
    """处理登录的CORS预检请求"""
    return {}


@router.post("/login/json", response_model=LoginResponse, summary="JSON格式登录")
async def login_json(
    user_login: UserLogin,
    db: Session = Depends(get_db)
) -> Any:
    """
    JSON格式用户登录
    
    支持用户名或邮箱登录
    """
    user = UserService.authenticate_user(db, user_login.username, user_login.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        subject=user.username, expires_delta=access_token_expires
    )
    
    # 创建刷新令牌
    refresh_token = create_refresh_token(subject=user.username)
    
    # 转换用户信息
    user_info = UserResponse.from_orm(user)
    
    return {
        "access_token": access_token,
        "token_type": "bearer", 
        "expires_in": settings.access_token_expire_minutes * 60,
        "refresh_token": refresh_token,
        "user_info": user_info
    }


@router.post("/register", response_model=UserResponse, summary="用户注册")
async def register(
    user_create: UserCreate,
    db: Session = Depends(get_db)
) -> Any:
    """
    用户注册
    
    创建新用户账户
    """
    try:
        user = UserService.create_user(db, user_create)
        return user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/refresh", response_model=Token, summary="刷新令牌")
async def refresh_token(
    refresh_token: str,
    db: Session = Depends(get_db)
) -> Any:
    """
    使用刷新令牌获取新的访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    username = verify_refresh_token(refresh_token)
    if not username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 验证用户是否存在且激活
    user = UserService.get_user_by_username(db, username)
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被停用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建新的访问令牌
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        subject=user.username, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.access_token_expire_minutes * 60
    }


@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def read_users_me(
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取当前认证用户的信息
    """
    return current_user


@router.post("/logout", summary="用户登出")
async def logout(
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    用户登出
    
    注意：JWT令牌是无状态的，真正的登出需要在客户端删除令牌
    或者实现令牌黑名单机制
    """
    return {"message": "登出成功"}


@router.post("/change-password", summary="修改密码")
async def change_password(
    current_password: str,
    new_password: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    修改当前用户密码
    
    - **current_password**: 当前密码
    - **new_password**: 新密码
    """
    # 验证当前密码
    if not UserService.authenticate_user(db, current_user.username, current_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )
    
    # 更新密码
    from ...schemas.user import UserUpdate
    user_update = UserUpdate(password=new_password)
    UserService.update_user(db, current_user.id, user_update)
    
    return {"message": "密码修改成功"} 
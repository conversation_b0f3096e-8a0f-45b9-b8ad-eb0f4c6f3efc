-- 创建项目表
-- 迁移文件: 002_create_projects_table.sql
-- 创建时间: 2025-07-15

-- 创建项目表
CREATE TABLE IF NOT EXISTS projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'new',
    created_by_user_id INTEGER NOT NULL,
    manager_id INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deadline TIMESTAMPTZ,
    
    -- 外键约束
    CONSTRAINT fk_projects_created_by_user 
        FOREIGN KEY (created_by_user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT fk_projects_manager 
        FOREIGN KEY (manager_id) 
        REFERENCES users(id) 
        ON DELETE SET NULL,
    
    -- 状态约束
    CONSTRAINT check_project_status 
        CHECK (status IN ('new', 'analyzing', 'outlining', 'drafting', 'reviewing', 'completed', 'archived'))
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_projects_name ON projects(name);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_by_user_id ON projects(created_by_user_id);
CREATE INDEX IF NOT EXISTS idx_projects_manager_id ON projects(manager_id);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_projects_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_projects_updated_at
    BEFORE UPDATE ON projects
    FOR EACH ROW
    EXECUTE FUNCTION update_projects_updated_at();

-- 插入测试数据
INSERT INTO projects (name, description, status, created_by_user_id, manager_id) VALUES
('医院信息化建设项目', '某三甲医院信息化系统建设投标项目', 'new', 1, 2),
('智慧城市监控系统', '城市智能监控系统采购项目', 'analyzing', 2, 2),
('教育云平台建设', '区域教育云平台建设项目', 'outlining', 1, 2)
ON CONFLICT DO NOTHING;

-- 验证表创建
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'projects' 
ORDER BY ordinal_position; 
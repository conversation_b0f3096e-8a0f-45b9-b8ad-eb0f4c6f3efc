import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  DatePicker,
  Select,
  message,
  Space,
  Row,
  Col,
  Typography,
  Alert,
  Steps,
} from 'antd';
import {
  SaveOutlined,
  ArrowLeftOutlined,
  ProjectOutlined,
  UserOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { projectAPI, userAPI, User, ApiResponse } from '../../services/api';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Paragraph } = Typography;

interface ProjectCreateForm {
  name: string;
  description?: string;
  manager_id?: number;
  deadline?: dayjs.Dayjs;
}

const ProjectCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm<ProjectCreateForm>();
  const [loading, setLoading] = useState(false);
  const [managers, setManagers] = useState<User[]>([]);
  const [managersLoading, setManagersLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  // 获取当前用户信息
  const getCurrentUser = (): User | null => {
    const userInfo = localStorage.getItem('user_info');
    return userInfo ? JSON.parse(userInfo) : null;
  };

  const currentUser = getCurrentUser();

  // 获取可用的项目经理列表
  const fetchManagers = async () => {
    try {
      setManagersLoading(true);
      const response = await userAPI.getUsers({ page_size: 100 });
      const result: ApiResponse<{ items: User[] }> = response.data;
      
      if (result.code === 200) {
        // 筛选出管理员和经理角色的用户
        const availableManagers = result.data.items.filter(
          user => user.role === 'admin' || user.role === 'manager'
        );
        setManagers(availableManagers);
      } else {
        message.error(result.message || '获取用户列表失败');
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
    } finally {
      setManagersLoading(false);
    }
  };

  // 创建项目
  const handleCreateProject = async (values: ProjectCreateForm) => {
    try {
      setLoading(true);
      
      const projectData = {
        name: values.name,
        description: values.description,
        manager_id: values.manager_id,
        deadline: values.deadline ? values.deadline.toISOString() : undefined,
      };

      const response = await projectAPI.createProject(projectData);
      const result = response.data;
      
      if (result.code === 200) {
        message.success('项目创建成功');
        navigate('/projects');
      } else {
        message.error(result.message || '创建项目失败');
      }
    } catch (error) {
      console.error('创建项目失败:', error);
      message.error('创建项目失败');
    } finally {
      setLoading(false);
    }
  };

  // 表单验证失败处理
  const handleFormError = (errorInfo: any) => {
    console.log('表单验证失败:', errorInfo);
    message.error('请检查表单输入');
  };

  // 步骤配置
  const steps = [
    {
      title: '基本信息',
      description: '填写项目名称和描述',
      icon: <ProjectOutlined />,
    },
    {
      title: '项目设置',
      description: '设置项目经理和截止时间',
      icon: <UserOutlined />,
    },
    {
      title: '确认创建',
      description: '确认信息并创建项目',
      icon: <InfoCircleOutlined />,
    },
  ];

  // 下一步
  const handleNext = () => {
    form.validateFields().then(() => {
      setCurrentStep(currentStep + 1);
    }).catch((errorInfo) => {
      console.log('表单验证失败:', errorInfo);
    });
  };

  // 上一步
  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  // 初始化数据
  useEffect(() => {
    fetchManagers();
    
    // 如果是经理角色，默认设置自己为项目经理
    if (currentUser?.role === 'manager') {
      form.setFieldsValue({
        manager_id: currentUser.id,
      });
    }
  }, []);

  // 权限检查
  if (!currentUser || (currentUser.role !== 'admin' && currentUser.role !== 'manager')) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="权限不足"
          description="只有管理员和经理可以创建项目"
          type="error"
          showIcon
        />
      </div>
    );
  }

  // 渲染基本信息步骤
  const renderBasicInfoStep = () => (
    <Card title="项目基本信息" style={{ marginBottom: 24 }}>
      <Form.Item
        name="name"
        label="项目名称"
        rules={[
          { required: true, message: '请输入项目名称' },
          { max: 255, message: '项目名称不能超过255个字符' },
        ]}
      >
        <Input
          placeholder="请输入项目名称"
          prefix={<ProjectOutlined />}
          size="large"
        />
      </Form.Item>

      <Form.Item
        name="description"
        label="项目描述"
        rules={[
          { max: 1000, message: '项目描述不能超过1000个字符' },
        ]}
      >
        <TextArea
          placeholder="请输入项目描述（可选）"
          rows={4}
          showCount
          maxLength={1000}
        />
      </Form.Item>
    </Card>
  );

  // 渲染项目设置步骤
  const renderProjectSettingsStep = () => (
    <Card title="项目设置" style={{ marginBottom: 24 }}>
      <Form.Item
        name="manager_id"
        label="项目经理"
        tooltip="选择负责此项目的经理，如不选择则默认为创建者"
      >
        <Select
          placeholder="请选择项目经理"
          allowClear
          loading={managersLoading}
          showSearch
          optionFilterProp="children"
          filterOption={(input, option) =>
            (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
          }
        >
          {managers.map(manager => (
            <Option key={manager.id} value={manager.id}>
              <Space>
                <UserOutlined />
                {manager.full_name || manager.username}
                <span style={{ color: '#666' }}>
                  ({manager.role === 'admin' ? '管理员' : '经理'})
                </span>
              </Space>
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        name="deadline"
        label="截止时间"
        tooltip="项目的预期完成时间"
      >
        <DatePicker
          placeholder="请选择截止时间"
          style={{ width: '100%' }}
          disabledDate={(current) => current && current < dayjs().startOf('day')}
          showTime
          format="YYYY-MM-DD HH:mm"
        />
      </Form.Item>

      <Alert
        message="项目创建说明"
        description={
          <div>
            <p>• 项目创建后将自动设置为"新建"状态</p>
            <p>• 项目经理可以管理项目的所有内容</p>
            <p>• 管理员可以查看和管理所有项目</p>
            <p>• 项目状态将随着工作流程自动更新</p>
          </div>
        }
        type="info"
        showIcon
        style={{ marginTop: 16 }}
      />
    </Card>
  );

  // 渲染确认步骤
  const renderConfirmStep = () => {
    const values = form.getFieldsValue();
    const selectedManager = managers.find(m => m.id === values.manager_id);
    
    return (
      <Card title="确认项目信息" style={{ marginBottom: 24 }}>
        <Row gutter={24}>
          <Col span={12}>
            <div style={{ marginBottom: 16 }}>
              <Title level={5}>项目名称</Title>
              <Paragraph>{values.name || '未填写'}</Paragraph>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <Title level={5}>项目描述</Title>
              <Paragraph>
                {values.description || '无描述'}
              </Paragraph>
            </div>
          </Col>
          
          <Col span={12}>
            <div style={{ marginBottom: 16 }}>
              <Title level={5}>项目经理</Title>
              <Paragraph>
                {selectedManager 
                  ? `${selectedManager.full_name || selectedManager.username} (${selectedManager.role === 'admin' ? '管理员' : '经理'})`
                  : '默认为创建者'
                }
              </Paragraph>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <Title level={5}>截止时间</Title>
              <Paragraph>
                {values.deadline 
                  ? values.deadline.format('YYYY-MM-DD HH:mm')
                  : '无截止时间'
                }
              </Paragraph>
            </div>
          </Col>
        </Row>

        <Alert
          message="确认创建项目"
          description="请确认以上信息无误，点击创建项目按钮完成创建。"
          type="success"
          showIcon
          style={{ marginTop: 16 }}
        />
      </Card>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/projects')}
          style={{ marginBottom: 16 }}
        >
          返回项目列表
        </Button>
        <Title level={2}>创建新项目</Title>
        <Paragraph type="secondary">
          创建一个新的投标项目，开始您的投标文档制作流程
        </Paragraph>
      </div>

      {/* 步骤指示器 */}
      <Card style={{ marginBottom: 24 }}>
        <Steps current={currentStep} items={steps} />
      </Card>

      {/* 表单内容 */}
      <Form
        form={form}
        layout="vertical"
        onFinish={handleCreateProject}
        onFinishFailed={handleFormError}
        requiredMark={false}
      >
        {currentStep === 0 && renderBasicInfoStep()}
        {currentStep === 1 && renderProjectSettingsStep()}
        {currentStep === 2 && renderConfirmStep()}

        {/* 操作按钮 */}
        <Card>
          <Row justify="space-between">
            <Col>
              {currentStep > 0 && (
                <Button onClick={handlePrev}>
                  上一步
                </Button>
              )}
            </Col>
            <Col>
              <Space>
                <Button onClick={() => navigate('/projects')}>
                  取消
                </Button>
                {currentStep < steps.length - 1 ? (
                  <Button type="primary" onClick={handleNext}>
                    下一步
                  </Button>
                ) : (
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<SaveOutlined />}
                  >
                    创建项目
                  </Button>
                )}
              </Space>
            </Col>
          </Row>
        </Card>
      </Form>
    </div>
  );
};

export default ProjectCreatePage; 
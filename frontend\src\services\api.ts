import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 过滤掉空字符串参数
    if (config.params) {
      Object.keys(config.params).forEach(key => {
        if (config.params[key] === '') {
          delete config.params[key];
        }
      });
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期，清除本地存储并跳转到登录页
      localStorage.removeItem('access_token');
      localStorage.removeItem('user_info');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 用户认证API
export const authAPI = {
  login: (credentials: { username: string; password: string }) =>
    api.post('/api/v1/auth/login/json', credentials),
  
  register: (userData: { username: string; email: string; password: string; full_name?: string }) =>
    api.post('/api/v1/auth/register', userData),
  
  getCurrentUser: () =>
    api.get('/api/v1/auth/me'),
  
  logout: () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('user_info');
    return Promise.resolve();
  },
};

// 项目管理API
export const projectAPI = {
  // 获取项目列表
  getProjects: (params?: {
    name?: string;
    status?: string;
    created_by_user_id?: number;
    manager_id?: number;
    page?: number;
    page_size?: number;
  }) => api.get('/api/v1/projects', { params }),
  
  // 获取项目详情
  getProject: (projectId: number) =>
    api.get(`/api/v1/projects/${projectId}`),
  
  // 创建项目
  createProject: (projectData: {
    name: string;
    description?: string;
    manager_id?: number;
    deadline?: string;
  }) => api.post('/api/v1/projects', projectData),
  
  // 更新项目
  updateProject: (projectId: number, projectData: {
    name?: string;
    description?: string;
    status?: string;
    manager_id?: number;
    deadline?: string;
  }) => api.put(`/api/v1/projects/${projectId}`, projectData),
  
  // 删除项目
  deleteProject: (projectId: number) =>
    api.delete(`/api/v1/projects/${projectId}`),
  
  // 更新项目状态
  updateProjectStatus: (projectId: number, status: string) =>
    api.patch(`/api/v1/projects/${projectId}/status`, { status }),
  
  // 分配项目经理
  assignManager: (projectId: number, managerId: number) =>
    api.patch(`/api/v1/projects/${projectId}/assign-manager`, { manager_id: managerId }),
  
  // 获取项目统计
  getProjectStatistics: () =>
    api.get('/api/v1/projects/statistics/overview'),
  
  // 获取我创建的项目
  getMyCreatedProjects: (params?: { page?: number; page_size?: number }) =>
    api.get('/api/v1/projects/my/created', { params }),
  
  // 获取我管理的项目
  getMyManagedProjects: (params?: { page?: number; page_size?: number }) =>
    api.get('/api/v1/projects/my/managed', { params }),
};

// 用户管理API
export const userAPI = {
  // 获取用户列表
  getUsers: (params?: { page?: number; page_size?: number }) =>
    api.get('/api/v1/users', { params }),
  
  // 获取用户信息
  getUser: (userId: number) =>
    api.get(`/api/v1/users/${userId}`),
};

// 类型定义
export interface User {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'manager' | 'specialist';
  full_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Project {
  id: number;
  name: string;
  description?: string;
  status: 'new' | 'analyzing' | 'outlining' | 'drafting' | 'reviewing' | 'completed' | 'archived';
  created_by_user_id: number;
  manager_id?: number;
  created_at: string;
  updated_at: string;
  deadline?: string;
}

export interface ProjectListResponse {
  code: number;
  message: string;
  data: {
    items: Project[];
    pagination: {
      page: number;
      page_size: number;
      total: number;
      pages: number;
    };
  };
}

export interface ProjectResponse {
  code: number;
  message: string;
  data: Project;
}

export interface ProjectStatistics {
  total_projects: number;
  status_counts: Record<string, number>;
  user_role: string;
}

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export default api; 
#!/usr/bin/env python3
"""
使用SQLite创建测试用户脚本
适用于本地开发和测试
"""

import sqlite3
import os
from pathlib import Path
from passlib.context import CryptContext

# 密码加密配置
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 数据库文件路径
DB_FILE = Path(__file__).parent.parent / "intellibid.db"

def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)

def create_test_users():
    """创建测试用户"""
    print("正在创建SQLite数据库...")
    
    # 测试用户数据
    test_users = [
        {
            "username": "admin",
            "password": "admin123",
            "role": "admin",
            "description": "系统管理员"
        },
        {
            "username": "manager",
            "password": "manager123", 
            "role": "manager",
            "description": "项目经理"
        },
        {
            "username": "specialist",
            "password": "spec123",
            "role": "specialist", 
            "description": "专员"
        },
        {
            "username": "testuser",
            "password": "test123",
            "role": "specialist",
            "description": "测试用户"
        }
    ]
    
    try:
        # 连接SQLite数据库
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        print(f"✅ SQLite数据库创建: {DB_FILE}")
        
        # 创建users表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            hashed_password TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'specialist',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        cursor.execute(create_table_sql)
        print("✅ 用户表创建完成")
        
        # 创建测试用户
        for user_data in test_users:
            # 检查用户是否已存在
            cursor.execute("SELECT username FROM users WHERE username = ?", (user_data["username"],))
            existing = cursor.fetchone()
            
            if existing:
                print(f"⚠️  用户 {user_data['username']} 已存在，跳过创建")
                continue
            
            # 创建新用户
            hashed_password = get_password_hash(user_data["password"])
            cursor.execute(
                "INSERT INTO users (username, hashed_password, role) VALUES (?, ?, ?)",
                (user_data["username"], hashed_password, user_data["role"])
            )
            
            print(f"✅ 创建用户: {user_data['username']} ({user_data['description']}) - 密码: {user_data['password']}")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 测试用户创建完成！")
        print("\n📋 测试账号列表:")
        print("=" * 60)
        print("| 用户名      | 密码        | 角色        | 描述         |")
        print("=" * 60)
        for user_data in test_users:
            print(f"| {user_data['username']:<10} | {user_data['password']:<10} | {user_data['role']:<10} | {user_data['description']:<10} |")
        print("=" * 60)
        print(f"\n📁 数据库文件: {DB_FILE}")
        print("\n🌐 访问地址:")
        print("- 前端: http://localhost:3001")
        print("- 后端API: http://localhost:8000")
        print("- API文档: http://localhost:8000/docs")
        print("\n💡 使用方法:")
        print("1. 修改 backend/app/core/config.py 中的数据库URL为SQLite")
        print(f"   database_url = 'sqlite:///{DB_FILE}'")
        print("2. 启动后端: python main.py")
        print("3. 启动前端: cd ../frontend && npm run dev")
        print("4. 访问前端进行登录测试")
        
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    create_test_users() 
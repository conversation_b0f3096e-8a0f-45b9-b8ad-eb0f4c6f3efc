### **智能标书生成系统：系统架构设计说明书**

#### **1. 架构愿景与设计原则**

本系统旨在成为一个企业级的、由AI驱动的智能内容创作与协同平台。为实现这一愿景，系统架构将遵循以下核心设计原则：

- **分层与模块化 (Layered & Modular)**: 严格遵循关注点分离原则，将系统划分为清晰的层次和独立的模块，便于开发、测试、维护和独立升级。
- **服务化 (Service-Oriented)**: 将核心的、资源密集型或需要独立伸缩的功能（如LLM调用、文档转换）封装成独立的服务，通过定义良好的API进行交互。
- **高可用与可扩展 (High Availability & Scalability)**: 架构设计需支持水平扩展，确保在用户量和数据量增长时系统性能稳定，并能通过冗余部署避免单点故障。
- **安全优先 (Security First)**: 将数据安全和访问控制贯穿于架构的每一层，特别是在处理敏感的投标数据和私有模型调用时。
- **可配置与可维护 (Configurable & Maintainable)**: 关键业务逻辑，尤其是Prompt模板和LLM引擎配置，必须外部化，允许非开发人员进行迭代优化。

#### **2. 总体架构视图**

本系统采用**分层与模块化的服务架构**。该架构由四大核心层次构成：**前端应用层、应用服务层、核心引擎层、数据与存储层**，并通过标准化的API进行通信。

------

#### **3. 各层详细设计**

##### **3.1. 前端应用层 (Frontend Application Layer)**

此层是用户与系统交互的入口，负责提供流畅、直观的用户体验。

- **组件构成**:
  1. **用户工作台 (User Workbench)**: 面向投标团队的核心操作界面，采用单页应用（SPA）技术构建。
     - **需求映射**: F-1.1, F-1.4, F-2.3, F-3.5
     - **核心模块**:
       - **文件上传模块**: 支持 `.docx`, `.pdf` 文件的拖拽上传与进度显示。
       - **分析报告视图**: 以交互式界面展示解析后的结构化报告。
       - **大纲编辑器**: 支持对生成的大纲进行拖拽排序、编辑和指派。
       - **ProseMirror编辑器**: 系统的核心，实现了自定义的结构化文档Schema，并集成了丰富的AI辅助创作功能。
  2. **管理后台 (Admin Dashboard)**: 面向系统管理员的独立SPA，用于系统配置和监控。
     - **需求映射**: F-3.4.1, F-3.4.2, DA-1, NF-6
     - **核心模块**:
       - **LLM引擎配置中心**: 增、删、改、查支持的LLM模型，并设置默认模型。
       - **知识库管理模块**: 上传、管理企业知识库的源文档。
       - **成本监控仪表盘**: 展示在线LLM的费用消耗情况。
- **技术选型推荐**:
  - **框架**: React (Vite) 或 Vue 3
  - **状态管理**: Redux Toolkit 或 Pinia
  - **UI库**: Ant Design Pro 或 Element Plus
  - **编辑器**: ProseMirror 工具集

##### **3.2. 应用服务层 (Application Service Layer)**

此层是系统的“大脑”，负责处理所有业务逻辑、工作流编排和用户管理。

- **组件构成**:
  1. **API网关 (API Gateway)**: 作为所有前端请求的统一入口，负责请求路由、认证鉴权、速率限制和日志记录。
  2. **工作流编排服务 (Workflow Orchestration Service)**:
     - **需求映射**: 核心流程1, 2, 3
     - **职责**: 实现核心的LangChain业务逻辑。接收前端请求后，按顺序调用文档解析、LLM分析、知识库检索、内容生成等下游服务，并将结果组合后返回。
  3. **用户与权限管理服务 (User & Auth Service)**:
     - **需求映射**: 第二章用户角色, NF-3
     - **职责**: 管理用户账户、角色（总监、经理、专员、管理员）以及对应的操作权限。
  4. **项目与文档管理服务 (Project & Document Service)**:
     - **需求映射**: F-1.5
     - **职责**: 负责项目、上传的招标文档、生成的分析报告和标书内容的CRUD（增删改查）操作及元数据管理。
- **技术选型推荐**:
  - **语言/框架**: Python 3.10+，使用 FastAPI (高性能) 或 Django (功能全面)。
  - **API通信**: RESTful API 或 GraphQL。

##### **3.3. 核心引擎层 (Core Engine Layer)**

此层由一系列独立的、专用的后台服务组成，负责执行资源密集型的核心任务。

- **组件构成**:
  1. **文档解析引擎 (Document Parsing Engine)**:
     - **需求映射**: F-1.2
     - **职责**: 接收文件存储路径，调用Unstructured、PyPDF等库，将文档转换为纯文本或结构化文本（如Markdown），并返回给工作流编排服务。建议部署为独立的容器化服务。
  2. **LLM服务层 (LLM Service Layer)**:
     - **需求映射**: F-3.4, IN-1, NF-5
     - **职责**: 实现**适配器模式**，对外提供统一的LLM调用接口。
     - **内部模块**:
       - **LLM网关**: 接收内部调用请求，根据`model_id`路由到相应的适配器。
       - **模型适配器 (Adapters)**: 为每个LLM（DeepSeek, OpenAI, 私有模型等）提供一个具体的实现，负责转换API请求和响应格式。
  3. **文档导出服务 (Document Export Service)**:
     - **需求映射**: F-3.5.4
     - **职责**: 接收ProseMirror的文档JSON和导出类型（PDF/DOCX），在服务端进行高保真转换。这是一个独立的、可水平扩展的服务。
     - **内部工具**: Puppeteer/Playwright (用于PDF), `python-docx` (用于Word)。
- **技术选型推荐**:
  - **服务框架**: Python (FastAPI)。
  - **AI框架**: LangChain, PyTorch。
  - **容器化**: Docker。

##### **3.4. 数据与存储层 (Data & Storage Layer)**

此层负责所有数据的持久化，为上层应用提供可靠的数据支持。

- **组件构成**:
  1. **关系型数据库 (SQL Database)**:
     - **职责**: 存储结构化数据，如用户信息、项目信息、文档元数据、LLM配置、成本日志等。
     - **推荐**: pgvector(PostgreSQL)。
  2. **向量数据库 (Vector Database)**:
     - **需求映射**: DA-1, F-3.2
     - **职责**: 存储企业知识库文档的向量嵌入（Embeddings），用于RAG的快速相似性检索。
     - **推荐**: pgvector。
  3. **对象存储 (Object Storage)**:
     - **职责**: 存储大型二进制文件，如上传的原始招标文档、导出的PDF/Word文件、知识库源文件等。
     - **推荐**: MinIO。
  4. **缓存 (Cache)**:
     - **职责**: 存储临时会话数据、热点数据，提高系统响应速度。
     - **推荐**: Redis。

------

#### **4. 数据流示例：编辑器内“扩写内容”**

1. **[前端]** 用户在ProseMirror编辑器中选中一段文字，点击“扩写内容”。
2. **[前端]** 编辑器插件捕获该事件，将选中的文本和“expand”指令，通过API网关发送到**工作流编排服务**。
3. **[应用服务层]** **工作流编排服务**接收请求，构造一个包含上下文（如项目背景、章节主题）和用户选文的Prompt。
4. **[应用服务层]** 它向**LLM服务层**发起调用，指定`model_id`（用户选择的或默认的）和构造好的Prompt。
5. **[核心引擎层]** **LLM服务层**的网关根据`model_id`选择对应的**模型适配器**（如`DeepSeekAdapter`）。
6. **[核心引擎层]** **适配器**将请求格式化为DeepSeek API要求的格式，并发送请求。
7. **[外部/私有]** DeepSeek LLM处理请求并返回扩写后的文本。
8. **[数据流返回]** 结果沿原路返回至**工作流编排服务**。
9. **[应用服务层]** 服务将结果通过API返回给前端。
10. **[前端]** 编辑器插件接收到扩写后的文本，并使用ProseMirror的事务（Transaction）功能，平滑地替换掉用户原先选中的文本。

------

#### **5. 部署与运维 (Infrastructure & DevOps)**

- **部署架构**:
  - 所有服务（前端、后端、引擎）均应**容器化（Docker）**。
  - 使用 **Kubernetes (K8s)** 进行容器编排，实现服务的弹性伸缩、自动恢复和滚动更新。
  - 私有LLM服务可部署在带有GPU的专属K8s节点组上。
- **CI/CD**:
  - 建立自动化的持续集成/持续部署流水线（如使用GitHub Actions, Jenkins），实现代码提交后自动测试和部署。
- **监控与告警**:
  - 建立全面的监控体系（如使用Prometheus + Grafana），监控各服务的健康状况、性能指标（QPS, 延迟）和资源使用率。
  - 针对关键业务（如导出失败、LLM API调用错误）和资源（如成本超预算）建立告警机制。
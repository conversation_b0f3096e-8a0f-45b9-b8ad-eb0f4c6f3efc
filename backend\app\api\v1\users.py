"""
用户管理API路由
包括用户CRUD操作等功能
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ...database import get_db
from ...services.user_service import UserService
from ...schemas.user import UserCreate, UserUpdate, UserResponse
from ...core.deps import get_current_user, require_admin, require_manager
from ...models.user import User, UserRole

router = APIRouter()


@router.get("/", response_model=List[UserResponse], summary="获取用户列表")
async def read_users(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    role: Optional[UserRole] = Query(None, description="角色过滤"),
    is_active: Optional[bool] = Query(None, description="激活状态过滤"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_manager)
) -> Any:
    """
    获取用户列表
    
    需要经理或管理员权限
    """
    users = UserService.get_users(
        db, skip=skip, limit=limit, role=role, is_active=is_active
    )
    return users


@router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def read_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_manager)
) -> Any:
    """
    根据ID获取用户详情
    
    需要经理或管理员权限
    """
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


@router.post("/", response_model=UserResponse, summary="创建用户")
async def create_user(
    user_create: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
) -> Any:
    """
    创建新用户
    
    需要管理员权限
    """
    try:
        user = UserService.create_user(db, user_create)
        return user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    更新用户信息
    
    用户只能更新自己的信息，管理员可以更新任何用户
    """
    # 检查权限
    if user_id != current_user.id and not current_user.has_permission(UserRole.ADMIN):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只能更新自己的信息"
        )
    
    # 非管理员不能修改角色
    if user_update.role is not None and not current_user.has_permission(UserRole.ADMIN):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改用户角色"
        )
    
    try:
        user = UserService.update_user(db, user_id, user_update)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        return user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{user_id}", summary="停用用户")
async def deactivate_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
) -> Any:
    """
    停用用户账户
    
    需要管理员权限
    """
    # 不能停用自己
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能停用自己的账户"
        )
    
    success = UserService.deactivate_user(db, user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return {"message": "用户已停用"}


@router.post("/{user_id}/activate", summary="激活用户")
async def activate_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
) -> Any:
    """
    激活用户账户
    
    需要管理员权限
    """
    user_update = UserUpdate(is_active=True)
    user = UserService.update_user(db, user_id, user_update)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return {"message": "用户已激活"}


@router.get("/profile/me", response_model=UserResponse, summary="获取个人资料")
async def get_my_profile(
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取当前用户的个人资料
    """
    return current_user


@router.put("/profile/me", response_model=UserResponse, summary="更新个人资料")
async def update_my_profile(
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    更新当前用户的个人资料
    
    普通用户不能修改角色和激活状态
    """
    # 限制普通用户不能修改的字段
    if not current_user.has_permission(UserRole.ADMIN):
        user_update.role = None
        user_update.is_active = None
    
    try:
        user = UserService.update_user(db, current_user.id, user_update)
        return user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        ) 
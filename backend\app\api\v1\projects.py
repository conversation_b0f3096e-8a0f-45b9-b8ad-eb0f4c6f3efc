"""
项目管理API
提供项目的CRUD操作和管理功能
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ...database import get_db
from ...core.rbac import (
    require_admin,
    require_manager,
    require_any_role,
    get_current_user_from_token
)
from ...models.user import User
from ...models.project import ProjectStatus
from ...schemas.project import (
    ProjectCreate,
    ProjectUpdate,
    ProjectResponse,
    ProjectListResponse,
    ProjectDetailResponse,
    ProjectStatusUpdate,
    ProjectAssignManager,
    ProjectQuery
)
from ...services.project_service import ProjectService

router = APIRouter()


@router.post("/", response_model=Dict[str, Any])
async def create_project(
    project_data: ProjectCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_manager())
):
    """
    创建新项目
    
    **权限要求**: 经理或管理员
    
    - 经理: 可以创建项目并指定自己或其他经理为项目经理
    - 管理员: 可以创建项目并指定任何经理为项目经理
    """
    try:
        # 添加调试日志
        print(f"收到项目创建请求: {project_data}")
        print(f"当前用户: {current_user.username} (角色: {current_user.role})")

        project = ProjectService.create_project(db, project_data, current_user)
        return {
            "code": 200,
            "message": "项目创建成功",
            "data": ProjectResponse.from_orm(project)
        }
    except HTTPException:
        raise
    except Exception as e:
        print(f"项目创建异常: {str(e)}")
        print(f"异常类型: {type(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建项目失败: {str(e)}"
        )


@router.get("/", response_model=Dict[str, Any])
async def list_projects(
    name: str = Query(None, description="项目名称搜索"),
    status_filter: ProjectStatus = Query(None, alias="status", description="项目状态过滤"),
    created_by_user_id: int = Query(None, description="创建者ID过滤"),
    manager_id: int = Query(None, description="经理ID过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role())
):
    """
    获取项目列表
    
    **权限要求**: 任何已认证用户
    
    - 管理员: 可以查看所有项目
    - 经理: 可以查看自己管理的项目和自己创建的项目
    - 专员: 可以查看分配给自己的项目
    """
    try:
        query = ProjectQuery(
            name=name,
            status=status_filter,
            created_by_user_id=created_by_user_id,
            manager_id=manager_id,
            page=page,
            page_size=page_size
        )
        
        result = ProjectService.get_user_projects(db, current_user, query)
        
        return {
            "code": 200,
            "message": "获取项目列表成功",
            "data": {
                "items": [ProjectListResponse.from_orm(project) for project in result["items"]],
                "pagination": result["pagination"]
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目列表失败: {str(e)}"
        )


@router.get("/{project_id}", response_model=Dict[str, Any])
async def get_project(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role())
):
    """
    获取项目详情
    
    **权限要求**: 任何已认证用户，但只能访问有权限的项目
    
    - 管理员: 可以查看任何项目
    - 经理: 可以查看自己管理的项目和自己创建的项目
    - 专员: 可以查看分配给自己的项目
    """
    try:
        project = ProjectService.get_project_by_id(db, project_id, current_user)
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目不存在"
            )
        
        return {
            "code": 200,
            "message": "获取项目详情成功",
            "data": ProjectDetailResponse.from_orm(project)
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目详情失败: {str(e)}"
        )


@router.put("/{project_id}", response_model=Dict[str, Any])
async def update_project(
    project_id: int,
    project_data: ProjectUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role())
):
    """
    更新项目信息
    
    **权限要求**: 项目经理或管理员
    
    - 管理员: 可以更新任何项目
    - 经理: 可以更新自己管理的项目
    - 专员: 无权限更新项目
    """
    try:
        project = ProjectService.update_project(db, project_id, project_data, current_user)
        
        return {
            "code": 200,
            "message": "项目更新成功",
            "data": ProjectResponse.from_orm(project)
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新项目失败: {str(e)}"
        )


@router.delete("/{project_id}", response_model=Dict[str, Any])
async def delete_project(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin())
):
    """
    删除项目
    
    **权限要求**: 仅管理员
    
    - 管理员: 可以删除任何项目
    - 经理: 无权限删除项目
    - 专员: 无权限删除项目
    """
    try:
        success = ProjectService.delete_project(db, project_id, current_user)
        
        if success:
            return {
                "code": 200,
                "message": "项目删除成功",
                "data": {"project_id": project_id}
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除项目失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除项目失败: {str(e)}"
        )


@router.patch("/{project_id}/status", response_model=Dict[str, Any])
async def update_project_status(
    project_id: int,
    status_data: ProjectStatusUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role())
):
    """
    更新项目状态
    
    **权限要求**: 项目经理或管理员
    
    - 管理员: 可以更新任何项目状态
    - 经理: 可以更新自己管理的项目状态
    - 专员: 无权限更新项目状态
    """
    try:
        project = ProjectService.update_project_status(
            db, project_id, status_data.status, current_user
        )
        
        return {
            "code": 200,
            "message": "项目状态更新成功",
            "data": ProjectResponse.from_orm(project)
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新项目状态失败: {str(e)}"
        )


@router.patch("/{project_id}/assign-manager", response_model=Dict[str, Any])
async def assign_project_manager(
    project_id: int,
    assign_data: ProjectAssignManager,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin())
):
    """
    分配项目经理
    
    **权限要求**: 仅管理员
    
    - 管理员: 可以为任何项目分配经理
    - 经理: 无权限分配项目经理
    - 专员: 无权限分配项目经理
    """
    try:
        project = ProjectService.assign_manager(
            db, project_id, assign_data.manager_id, current_user
        )
        
        return {
            "code": 200,
            "message": "项目经理分配成功",
            "data": ProjectResponse.from_orm(project)
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分配项目经理失败: {str(e)}"
        )


@router.get("/statistics/overview", response_model=Dict[str, Any])
async def get_project_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role())
):
    """
    获取项目统计信息
    
    **权限要求**: 任何已认证用户
    
    - 管理员: 可以查看所有项目统计
    - 经理: 可以查看自己管理的项目统计
    - 专员: 可以查看自己的项目统计
    """
    try:
        statistics = ProjectService.get_project_statistics(db, current_user)
        
        return {
            "code": 200,
            "message": "获取项目统计成功",
            "data": statistics
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目统计失败: {str(e)}"
        )


@router.get("/my/created", response_model=Dict[str, Any])
async def get_my_created_projects(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_any_role())
):
    """
    获取我创建的项目
    
    **权限要求**: 任何已认证用户
    """
    try:
        query = ProjectQuery(
            created_by_user_id=current_user.id,
            page=page,
            page_size=page_size
        )
        
        result = ProjectService.get_user_projects(db, current_user, query)
        
        return {
            "code": 200,
            "message": "获取我创建的项目成功",
            "data": {
                "items": [ProjectListResponse.from_orm(project) for project in result["items"]],
                "pagination": result["pagination"]
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取我创建的项目失败: {str(e)}"
        )


@router.get("/my/managed", response_model=Dict[str, Any])
async def get_my_managed_projects(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_manager())
):
    """
    获取我管理的项目
    
    **权限要求**: 经理或管理员
    """
    try:
        query = ProjectQuery(
            manager_id=current_user.id,
            page=page,
            page_size=page_size
        )
        
        result = ProjectService.get_user_projects(db, current_user, query)
        
        return {
            "code": 200,
            "message": "获取我管理的项目成功",
            "data": {
                "items": [ProjectListResponse.from_orm(project) for project in result["items"]],
                "pagination": result["pagination"]
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取我管理的项目失败: {str(e)}"
        ) 
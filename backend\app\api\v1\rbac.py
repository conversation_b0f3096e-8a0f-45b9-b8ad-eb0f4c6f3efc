"""
RBAC测试API
展示不同权限级别的端点使用示例
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any

from ...database import get_db
from ...core.rbac import (
    require_admin,
    require_manager,
    require_specialist,
    require_any_role,
    require_roles,
    get_current_user_from_token,
    get_user_permissions,
    check_user_permission
)
from ...models.user import User, UserRole
from ...schemas.user import UserResponse

router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user_from_token)
):
    """
    获取当前用户信息 (任何已认证用户)
    """
    return UserResponse.from_orm(current_user)


@router.get("/me/permissions")
async def get_my_permissions(
    current_user: User = Depends(get_current_user_from_token)
) -> Dict[str, Any]:
    """
    获取当前用户的权限信息 (任何已认证用户)
    """
    permissions = get_user_permissions(current_user)
    return {
        "code": 200,
        "message": "success",
        "data": permissions
    }


@router.get("/admin-only")
async def admin_only_endpoint(
    current_user: User = Depends(require_admin())
) -> Dict[str, Any]:
    """
    仅管理员可访问的端点
    """
    return {
        "code": 200,
        "message": "success",
        "data": {
            "message": "你好，管理员！",
            "user": current_user.username,
            "role": current_user.role
        }
    }


@router.get("/manager-and-above")
async def manager_and_above_endpoint(
    current_user: User = Depends(require_manager())
) -> Dict[str, Any]:
    """
    经理及以上权限可访问的端点 (管理员也可以访问)
    """
    return {
        "code": 200,
        "message": "success",
        "data": {
            "message": "你好，经理或管理员！",
            "user": current_user.username,
            "role": current_user.role
        }
    }


@router.get("/specialist-and-above")
async def specialist_and_above_endpoint(
    current_user: User = Depends(require_specialist())
) -> Dict[str, Any]:
    """
    专员及以上权限可访问的端点 (经理和管理员也可以访问)
    """
    return {
        "code": 200,
        "message": "success",
        "data": {
            "message": "你好，专员、经理或管理员！",
            "user": current_user.username,
            "role": current_user.role
        }
    }


@router.get("/any-authenticated")
async def any_authenticated_endpoint(
    current_user: User = Depends(require_any_role())
) -> Dict[str, Any]:
    """
    任何已认证用户可访问的端点
    """
    return {
        "code": 200,
        "message": "success",
        "data": {
            "message": "你好，已认证用户！",
            "user": current_user.username,
            "role": current_user.role
        }
    }


@router.get("/multiple-roles")
async def multiple_roles_endpoint(
    current_user: User = Depends(require_roles(UserRole.ADMIN, UserRole.MANAGER))
) -> Dict[str, Any]:
    """
    多个角色权限检查示例 (管理员或经理可访问)
    """
    return {
        "code": 200,
        "message": "success",
        "data": {
            "message": "你好，管理员或经理！",
            "user": current_user.username,
            "role": current_user.role
        }
    }


@router.get("/exact-role-match")
async def exact_role_match_endpoint(
    current_user: User = Depends(require_roles(UserRole.MANAGER, exact_match=True))
) -> Dict[str, Any]:
    """
    精确角色匹配示例 (仅经理可访问，管理员不可以)
    """
    return {
        "code": 200,
        "message": "success",
        "data": {
            "message": "你好，经理！(仅经理可访问)",
            "user": current_user.username,
            "role": current_user.role
        }
    }


@router.post("/check-permission")
async def check_permission_endpoint(
    permission_data: Dict[str, Any],
    current_user: User = Depends(get_current_user_from_token),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    检查用户权限的工具端点
    
    Body:
    {
        "required_roles": ["admin", "manager"],
        "exact_match": false
    }
    """
    try:
        required_roles_str = permission_data.get("required_roles", [])
        exact_match = permission_data.get("exact_match", False)
        
        # 转换字符串角色为枚举
        required_roles = []
        for role_str in required_roles_str:
            try:
                required_roles.append(UserRole(role_str))
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的角色: {role_str}"
                )
        
        # 检查权限
        has_permission = check_user_permission(current_user, required_roles, exact_match)
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "user": current_user.username,
                "user_role": current_user.role,
                "required_roles": required_roles_str,
                "exact_match": exact_match,
                "has_permission": has_permission
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"权限检查失败: {str(e)}"
        )


@router.get("/system-info")
async def system_info_endpoint(
    current_user: User = Depends(require_admin())
) -> Dict[str, Any]:
    """
    系统信息端点 (仅管理员可访问)
    """
    return {
        "code": 200,
        "message": "success",
        "data": {
            "message": "系统信息",
            "rbac_version": "1.0",
            "supported_roles": [role.value for role in UserRole],
            "role_hierarchy": {
                "admin": 3,
                "manager": 2,
                "specialist": 1
            },
            "accessed_by": {
                "user": current_user.username,
                "role": current_user.role
            }
        }
    }


@router.get("/role-demo/{demo_type}")
async def role_demo_endpoint(
    demo_type: str,
    current_user: User = Depends(get_current_user_from_token)
) -> Dict[str, Any]:
    """
    角色演示端点，根据demo_type动态检查权限
    
    demo_type:
    - admin: 需要管理员权限
    - manager: 需要经理权限
    - specialist: 需要专员权限
    - any: 任何角色
    """
    user_role = UserRole(current_user.role)
    
    if demo_type == "admin":
        if not check_user_permission(current_user, [UserRole.ADMIN]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )
        message = "管理员功能演示"
    elif demo_type == "manager":
        if not check_user_permission(current_user, [UserRole.MANAGER]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要经理权限"
            )
        message = "经理功能演示"
    elif demo_type == "specialist":
        if not check_user_permission(current_user, [UserRole.SPECIALIST]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要专员权限"
            )
        message = "专员功能演示"
    elif demo_type == "any":
        message = "通用功能演示"
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的演示类型"
        )
    
    return {
        "code": 200,
        "message": "success",
        "data": {
            "demo_type": demo_type,
            "message": message,
            "user": current_user.username,
            "role": current_user.role
        }
    } 
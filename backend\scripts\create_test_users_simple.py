#!/usr/bin/env python3
"""
简化版创建测试用户脚本
直接使用SQL语句创建用户，不依赖ORM模型
"""

import asyncio
import asyncpg
from passlib.context import CryptContext

# 密码加密配置
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 数据库配置
DATABASE_URL = "*******************************************************************/postgres"

def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)

async def create_test_users():
    """创建测试用户"""
    print("正在连接数据库...")
    
    # 测试用户数据
    test_users = [
        {
            "username": "admin",
            "password": "admin123",
            "role": "admin",
            "description": "系统管理员"
        },
        {
            "username": "manager",
            "password": "manager123", 
            "role": "manager",
            "description": "项目经理"
        },
        {
            "username": "specialist",
            "password": "spec123",
            "role": "specialist", 
            "description": "专员"
        },
        {
            "username": "testuser",
            "password": "test123",
            "role": "specialist",
            "description": "测试用户"
        }
    ]
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ 数据库连接成功")
        
        # 创建users表（如果不存在）
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(255) UNIQUE NOT NULL,
            hashed_password VARCHAR(255) NOT NULL,
            role VARCHAR(50) NOT NULL DEFAULT 'specialist',
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        """
        
        await conn.execute(create_table_sql)
        print("✅ 用户表检查/创建完成")
        
        # 创建测试用户
        for user_data in test_users:
            # 检查用户是否已存在
            existing = await conn.fetchval(
                "SELECT username FROM users WHERE username = $1",
                user_data["username"]
            )
            
            if existing:
                print(f"⚠️  用户 {user_data['username']} 已存在，跳过创建")
                continue
            
            # 创建新用户
            hashed_password = get_password_hash(user_data["password"])
            await conn.execute(
                "INSERT INTO users (username, hashed_password, role) VALUES ($1, $2, $3)",
                user_data["username"],
                hashed_password,
                user_data["role"]
            )
            
            print(f"✅ 创建用户: {user_data['username']} ({user_data['description']}) - 密码: {user_data['password']}")
        
        await conn.close()
        
        print("\n🎉 测试用户创建完成！")
        print("\n📋 测试账号列表:")
        print("=" * 60)
        print("| 用户名      | 密码        | 角色        | 描述         |")
        print("=" * 60)
        for user_data in test_users:
            print(f"| {user_data['username']:<10} | {user_data['password']:<10} | {user_data['role']:<10} | {user_data['description']:<10} |")
        print("=" * 60)
        print("\n🌐 访问地址:")
        print("- 前端: http://localhost:3001")
        print("- 后端API: http://localhost:8000")
        print("- API文档: http://localhost:8000/docs")
        print("\n💡 使用方法:")
        print("1. 启动后端: python main.py")
        print("2. 启动前端: cd ../frontend && npm run dev")
        print("3. 访问前端进行登录测试")
        
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        print(f"请检查数据库连接: {DATABASE_URL}")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(create_test_users()) 
"""
数据库连接和会话管理
配置SQLAlchemy数据库连接、会话工厂和依赖注入
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator

from .core.config import settings

# 创建数据库引擎
engine = create_engine(
    settings.database_url,
    pool_pre_ping=True,  # 连接池预检查
    pool_recycle=300,    # 连接回收时间(秒)
    echo=settings.debug  # 开发环境下打印SQL语句
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:
    """
    数据库会话依赖注入
    用于FastAPI的Depends()依赖注入系统
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """创建所有数据库表"""
    # WHY: 导入所有模型以确保表被正确创建
    from .models import User  # noqa: F401
    
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """删除所有数据库表（仅用于测试）"""
    Base.metadata.drop_all(bind=engine) 
---
type: "always_apply"
---
# 智能标书生成系统项目概述

## 项目愿景
打造一个由AI驱动的、端到端的智能标书解决方案，实现**投标响应速度提升80%，标书质量和一致性显著提高，中标率稳步增长**的目标。

## 核心功能流程
1. **智能解析**: 自动提取并结构化呈现招标文档的关键信息、要求、评分标准和风险
2. **大纲生成**: 基于解析结果，智能生成面向得分的、结构清晰的投标书大纲
3. **内容生成**: 根据大纲章节，结合内部知识库，自动生成或辅助生成标书初稿

## 关键文档
- 需求规格书: [系统需求规则说明书.md](docs/系统需求规则说明书.md)
- 架构设计: [系统架构设计说明书.md](docs/系统架构设计说明书.md)
- 详细设计: [系统详细设计说明书.md](docs/系统详细设计说明书.md)
- 开发任务: [开发任务清单.md](docs/开发任务清单.md)

## 技术栈
- **前端**: React (Vite), ProseMirror编辑器, Ant Design Pro
- **后端**: Python 3.10+, FastAPI, LangChain
- **数据库**: PostgreSQL + pgvector
- **存储**: MinIO对象存储
- **AI**: 多LLM支持(DeepSeek, OpenAI等)
- **部署**: Docker, Kubernetes
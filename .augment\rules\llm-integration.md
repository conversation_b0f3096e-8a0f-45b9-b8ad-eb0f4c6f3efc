---
type: "always_apply"
---
# LLM集成指南

## 多LLM支持架构
基于 [系统需求规则说明书.md](docs/系统需求规则说明书.md) 中的LLM引擎管理需求，系统采用**适配器模式**实现多LLM支持。

## 适配器模式设计

### LLM服务层架构
```
[前端/工作流] -> [LLM网关] -> [模型适配器] -> [具体LLM API]
```

### 核心组件

#### LLM网关 (LLM Gateway)
- 接收统一格式的内部调用请求
- 根据`model_id`路由到对应的适配器
- 处理认证、限流和监控

#### 模型适配器 (Model Adapters)
为每个LLM提供具体的适配器实现：
- `DeepSeekAdapter` - DeepSeek API适配器
- `OpenAIAdapter` - OpenAI API适配器
- `PrivateModelAdapter` - 私有化部署模型适配器

### 适配器接口定义
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, AsyncGenerator

class LLMAdapter(ABC):
    @abstractmethod
    async def generate(self, prompt: str, **kwargs) -> str:
        pass
    
    @abstractmethod
    async def stream_generate(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        pass
```

## 支持的LLM类型

### 在线API模型
- **DeepSeek**: 系统默认模型，性价比高
- **OpenAI GPT-4**: 高质量输出，成本较高
- **Claude**: Anthropic的模型，适合长文本处理
- **智谱清言**: 国产模型，支持中文优化

### 私有化部署模型
- **本地部署的开源模型**: 如Llama, ChatGLM等
- **企业内网专用模型**: 确保数据不出网
- **GPU集群部署**: 支持大规模并发调用

## 配置管理

### LLM配置表结构
参考 [database-schema.md](.augment/rules/database-schema.md) 中的`llm_configs`表：

```sql
CREATE TABLE llm_configs (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(255) UNIQUE NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- 'online', 'private'
    api_endpoint VARCHAR(1024) NOT NULL,
    api_key_secret_name VARCHAR(255),
    is_default BOOLEAN DEFAULT FALSE
);
```

## 核心Prompt模板

### 分析报告生成 (Prompt_Step1_Analysis)
```python
ANALYSIS_PROMPT = """
你是一个专业的投标分析专家。请仔细分析以下招标文档，提取关键信息：

招标文档内容：
{document_text}

请按以下结构输出分析报告：
1. 项目基本信息
2. 资格要求
3. 技术要求
4. 商务要求
5. 评分标准
6. 风险点分析
7. 建议策略
"""
```

### 大纲生成 (Prompt_Step2_Outline)
```python
OUTLINE_PROMPT = """
基于以下分析报告，生成一份结构化的投标书大纲：

分析报告：
{analysis_report}

请生成包含以下特点的大纲：
1. 三级目录结构
2. 针对评分点的策略提示
3. 章节内容要求
4. 字数建议
"""
```

### 内容生成 (Prompt_Step3_ContentGeneration)
```python
CONTENT_PROMPT = """
请为投标书的指定章节生成内容：

章节信息：
- 章节标题：{chapter_title}
- 章节要求：{chapter_requirements}

参考资料：
{knowledge_base_materials}

项目背景：
{project_context}

请生成专业、准确、符合要求的章节内容。
"""
```

## 成本管理

### Token计量
- 精确记录每次API调用的Token用量
- 区分输入Token和输出Token
- 按模型类型计算成本

### 成本监控
- 实时成本统计
- 按日/月/项目维度分析
- 成本阈值告警
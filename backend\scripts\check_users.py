#!/usr/bin/env python3
"""
检查数据库中的用户表和用户数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.core.config import get_settings

def check_users():
    """检查数据库中的用户表和用户数据"""
    settings = get_settings()
    
    print(f"连接数据库: {settings.database_url}")
    
    try:
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        with engine.connect() as conn:
            # 检查用户表是否存在
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'users'
                );
            """))
            table_exists = result.scalar()
            
            if table_exists:
                print("✅ 用户表存在")
                
                # 查询用户表结构
                result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_name = 'users'
                    ORDER BY ordinal_position;
                """))
                
                print("\n📋 用户表结构:")
                for row in result:
                    print(f"  - {row.column_name}: {row.data_type} ({'NULL' if row.is_nullable == 'YES' else 'NOT NULL'})")
                
                # 查询用户数据
                result = conn.execute(text("SELECT id, username, email, role, full_name, is_active, created_at FROM users"))
                users = result.fetchall()
                
                print(f"\n👥 用户数据 (共 {len(users)} 条):")
                for user in users:
                    print(f"  - ID: {user.id}, 用户名: {user.username}, 邮箱: {user.email}, 角色: {user.role}, 姓名: {user.full_name}, 激活: {user.is_active}")
                
                # 测试admin用户的密码哈希
                result = conn.execute(text("SELECT hashed_password FROM users WHERE username = 'admin'"))
                admin_hash = result.scalar()
                if admin_hash:
                    print(f"\n🔑 admin用户密码哈希: {admin_hash}")
                else:
                    print("\n❌ 未找到admin用户")
                    
            else:
                print("❌ 用户表不存在")
                
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

if __name__ == "__main__":
    check_users() 
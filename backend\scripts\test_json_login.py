#!/usr/bin/env python3
"""
测试JSON格式登录端点
"""
import requests
import json

def test_json_login():
    """测试JSON格式登录端点"""
    base_url = "http://localhost:8000"
    
    # 测试JSON格式登录
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("🔐 测试JSON格式登录...")
    print(f"请求URL: {base_url}/api/v1/auth/login/json")
    print(f"请求数据: {json.dumps(login_data, indent=2)}")
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/auth/login/json",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ JSON格式登录成功!")
            print(f"访问令牌: {result.get('access_token', 'N/A')[:50]}...")
            print(f"令牌类型: {result.get('token_type', 'N/A')}")
            
            # 测试获取用户信息
            if result.get('access_token'):
                headers = {"Authorization": f"Bearer {result['access_token']}"}
                me_response = requests.get(f"{base_url}/api/v1/auth/me", headers=headers)
                
                print(f"\n👤 获取用户信息状态码: {me_response.status_code}")
                if me_response.status_code == 200:
                    user_info = me_response.json()
                    print("✅ 获取用户信息成功!")
                    print(f"用户名: {user_info.get('username', 'N/A')}")
                    print(f"角色: {user_info.get('role', 'N/A')}")
                else:
                    print(f"❌ 获取用户信息失败: {me_response.text}")
        else:
            print(f"❌ JSON格式登录失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_json_login() 
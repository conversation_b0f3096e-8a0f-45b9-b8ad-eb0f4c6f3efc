#!/usr/bin/env python3
"""
运行003_create_core_business_tables.sql迁移脚本
"""

import sys
import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_migration():
    """运行核心业务表迁移"""
    print("开始运行核心业务表迁移...")
    
    # 数据库连接参数
    db_params = {
        'host': 'localhost',
        'database': 'intellibid',
        'user': 'postgres',
        'password': 'postgres',
        'port': 5432
    }
    
    try:
        # 连接数据库
        conn = psycopg2.connect(**db_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 读取迁移脚本
        migration_file = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), 
            'migrations', 
            '003_create_core_business_tables.sql'
        )
        
        with open(migration_file, 'r', encoding='utf-8') as f:
            migration_sql = f.read()
        
        # 执行迁移
        print("执行迁移脚本...")
        cursor.execute(migration_sql)
        
        print("✅ 核心业务表迁移完成!")
        
        # 验证表是否创建成功
        table_names = [
            'tender_documents', 'analysis_reports', 'bid_documents',
            'bid_document_versions', 'document_exports', 'project_activity_logs'
        ]
        
        for table_name in table_names:
            cursor.execute(f"SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '{table_name}'")
            table_count = cursor.fetchone()[0]
            
            if table_count > 0:
                print(f"✅ {table_name} 表创建成功!")
            else:
                print(f"❌ {table_name} 表创建失败!")
        
        # 显示表结构信息
        print("\n" + "="*80)
        print("📋 核心业务表结构概览")
        print("="*80)
        
        for table_name in table_names:
            cursor.execute(f"""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = '{table_name}' 
                ORDER BY ordinal_position
            """)
            
            columns = cursor.fetchall()
            print(f"\n🗂️  {table_name.upper()} 表:")
            print("-" * 60)
            print(f"{'列名':<25} {'数据类型':<15} {'可空':<8} {'默认值'}")
            print("-" * 60)
            
            for col in columns:
                nullable = "是" if col[2] == "YES" else "否"
                default = col[3] if col[3] else ""
                print(f"{col[0]:<25} {col[1]:<15} {nullable:<8} {default}")
        
        # 显示外键约束
        print("\n" + "="*80)
        print("🔗 外键约束")
        print("="*80)
        
        cursor.execute("""
            SELECT 
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name,
                tc.constraint_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage ccu 
                ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name IN ('tender_documents', 'analysis_reports', 'bid_documents', 
                                'bid_document_versions', 'document_exports', 'project_activity_logs')
            ORDER BY tc.table_name, tc.constraint_name
        """)
        
        foreign_keys = cursor.fetchall()
        print(f"{'表名':<25} {'列名':<20} {'引用表':<20} {'引用列':<15}")
        print("-" * 80)
        
        for fk in foreign_keys:
            print(f"{fk[0]:<25} {fk[1]:<20} {fk[2]:<20} {fk[3]:<15}")
        
        # 显示测试数据
        print("\n" + "="*80)
        print("📊 测试数据统计")
        print("="*80)
        
        for table_name in table_names:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"{table_name:<30} {count:>10} 条记录")
        
        # 显示索引信息
        print("\n" + "="*80)
        print("🔍 索引信息")
        print("="*80)
        
        cursor.execute("""
            SELECT 
                t.relname as table_name,
                i.relname as index_name,
                a.attname as column_name
            FROM pg_class t, pg_class i, pg_index ix, pg_attribute a
            WHERE t.oid = ix.indrelid
            AND i.oid = ix.indexrelid
            AND a.attrelid = t.oid
            AND a.attnum = ANY(ix.indkey)
            AND t.relkind = 'r'
            AND t.relname IN ('tender_documents', 'analysis_reports', 'bid_documents', 
                            'bid_document_versions', 'document_exports', 'project_activity_logs')
            ORDER BY t.relname, i.relname
        """)
        
        indexes = cursor.fetchall()
        current_table = None
        
        for idx in indexes:
            if idx[0] != current_table:
                current_table = idx[0]
                print(f"\n📋 {current_table}:")
            print(f"  - {idx[1]} ({idx[2]})")
        
        cursor.close()
        conn.close()
        
        print("\n" + "="*80)
        print("🎉 核心业务表迁移全部完成!")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_migration() 
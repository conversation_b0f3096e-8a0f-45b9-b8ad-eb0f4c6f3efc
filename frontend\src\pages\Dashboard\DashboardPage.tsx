import React from 'react'
import { Row, Col, Card, Statistic, Typography, Space, Button } from 'antd'
import {
  ProjectOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title, Paragraph } = Typography

const DashboardPage: React.FC = () => {
  const navigate = useNavigate()

  const handleCreateProject = () => {
    navigate('/projects')
  }

  return (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div>
          <Title level={2}>工作台</Title>
          <Paragraph type="secondary">
            欢迎使用 IntelliBid 智能标书生成系统，开始您的高效投标之旅
          </Paragraph>
        </div>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总项目数"
                value={12}
                prefix={<ProjectOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="进行中项目"
                value={5}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="已完成项目"
                value={7}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="本月新增"
                value={3}
                prefix={<PlusOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} lg={16}>
            <Card title="快速开始" extra={<Button type="link">查看更多</Button>}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Card
                  type="inner"
                  size="small"
                  actions={[
                    <Button type="primary" onClick={handleCreateProject}>
                      创建项目
                    </Button>,
                  ]}
                >
                  <Card.Meta
                    title="创建新的投标项目"
                    description="上传招标文档，开始AI智能分析和标书生成流程"
                  />
                </Card>
                
                <Card type="inner" size="small">
                  <Card.Meta
                    title="查看项目进展"
                    description="跟踪项目状态，管理投标文档和团队协作"
                  />
                </Card>
                
                <Card type="inner" size="small">
                  <Card.Meta
                    title="系统设置"
                    description="配置LLM模型、知识库和用户权限"
                  />
                </Card>
              </Space>
            </Card>
          </Col>
          
          <Col xs={24} lg={8}>
            <Card title="系统状态">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>API服务</span>
                  <span style={{ color: '#52c41a' }}>正常</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>数据库</span>
                  <span style={{ color: '#52c41a' }}>正常</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>LLM服务</span>
                  <span style={{ color: '#52c41a' }}>正常</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>存储服务</span>
                  <span style={{ color: '#52c41a' }}>正常</span>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      </Space>
    </div>
  )
}

export default DashboardPage 
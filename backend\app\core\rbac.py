"""
基于角色的访问控制 (RBAC) 中间件
提供完整的权限检查和访问控制功能
"""

from typing import List, Callable, Union, Optional
from functools import wraps
from fastapi import HTTPException, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from ..database import get_db
from ..core.security import verify_token
from ..services.user_service import UserService
from ..models.user import User, UserRole

# HTTP Bearer认证方案
security = HTTPBearer()


class RBACMiddleware:
    """RBAC中间件类"""
    
    def __init__(self):
        self.role_hierarchy = {
            UserRole.ADMIN: 3,
            UserRole.MANAGER: 2,
            UserRole.SPECIALIST: 1,
        }
    
    def get_role_level(self, role: UserRole) -> int:
        """获取角色等级"""
        return self.role_hierarchy.get(role, 0)
    
    def check_permission(self, user_role: UserRole, required_roles: List[UserRole]) -> bool:
        """
        检查用户是否具有所需权限
        
        Args:
            user_role: 用户角色
            required_roles: 所需角色列表
        
        Returns:
            是否有权限
        """
        user_level = self.get_role_level(user_role)
        
        # 检查是否满足任一所需角色
        for required_role in required_roles:
            required_level = self.get_role_level(required_role)
            if user_level >= required_level:
                return True
        
        return False
    
    def check_exact_roles(self, user_role: UserRole, allowed_roles: List[UserRole]) -> bool:
        """
        检查用户是否具有确切的角色权限(不考虑层级)
        
        Args:
            user_role: 用户角色
            allowed_roles: 允许的角色列表
        
        Returns:
            是否有权限
        """
        return user_role in allowed_roles


# 全局RBAC中间件实例
rbac = RBACMiddleware()


def get_current_user_from_token(
    db: Session = Depends(get_db),
    token: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    从Token获取当前用户
    
    Args:
        db: 数据库会话
        token: JWT令牌
    
    Returns:
        当前用户对象
    
    Raises:
        HTTPException: 认证失败
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # 验证令牌
    username = verify_token(token.credentials)
    if username is None:
        raise credentials_exception
    
    # 获取用户
    user = UserService.get_user_by_username(db, username)
    if user is None:
        raise credentials_exception
    
    # 检查用户是否激活
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被停用"
        )
    
    return user


def require_roles(*roles: UserRole, exact_match: bool = False):
    """
    创建角色权限检查依赖项
    
    Args:
        *roles: 所需角色列表
        exact_match: 是否精确匹配角色(不考虑层级)
    
    Returns:
        权限检查依赖函数
    """
    def role_checker(current_user: User = Depends(get_current_user_from_token)) -> User:
        user_role = UserRole(current_user.role)
        
        if exact_match:
            has_permission = rbac.check_exact_roles(user_role, list(roles))
        else:
            has_permission = rbac.check_permission(user_role, list(roles))
        
        if not has_permission:
            role_names = [role.value for role in roles]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要以下角色之一: {', '.join(role_names)}"
            )
        
        return current_user
    
    return role_checker


def require_admin():
    """要求管理员权限"""
    return require_roles(UserRole.ADMIN)


def require_manager():
    """要求经理权限(管理员也可以)"""
    return require_roles(UserRole.MANAGER)


def require_manager_only():
    """仅要求经理权限(管理员不可以)"""
    return require_roles(UserRole.MANAGER, exact_match=True)


def require_specialist():
    """要求专员权限(经理和管理员也可以)"""
    return require_roles(UserRole.SPECIALIST)


def require_specialist_only():
    """仅要求专员权限(经理和管理员不可以)"""
    return require_roles(UserRole.SPECIALIST, exact_match=True)


def require_any_role():
    """要求任意角色(已登录即可)"""
    return require_roles(UserRole.ADMIN, UserRole.MANAGER, UserRole.SPECIALIST)


def permission_required(*roles: UserRole, exact_match: bool = False):
    """
    权限装饰器，用于装饰路由函数
    
    Args:
        *roles: 所需角色列表
        exact_match: 是否精确匹配角色
    
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未认证用户"
                )
            
            user_role = UserRole(current_user.role)
            
            if exact_match:
                has_permission = rbac.check_exact_roles(user_role, list(roles))
            else:
                has_permission = rbac.check_permission(user_role, list(roles))
            
            if not has_permission:
                role_names = [role.value for role in roles]
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要以下角色之一: {', '.join(role_names)}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    
    return decorator


def check_user_permission(user: User, required_roles: List[UserRole], exact_match: bool = False) -> bool:
    """
    检查用户权限的工具函数
    
    Args:
        user: 用户对象
        required_roles: 所需角色列表
        exact_match: 是否精确匹配角色
    
    Returns:
        是否有权限
    """
    if not user or not user.is_active:
        return False
    
    user_role = UserRole(user.role)
    
    if exact_match:
        return rbac.check_exact_roles(user_role, required_roles)
    else:
        return rbac.check_permission(user_role, required_roles)


def get_user_permissions(user: User) -> dict:
    """
    获取用户的权限信息
    
    Args:
        user: 用户对象
    
    Returns:
        权限信息字典
    """
    if not user or not user.is_active:
        return {"permissions": [], "role_level": 0}
    
    user_role = UserRole(user.role)
    role_level = rbac.get_role_level(user_role)
    
    # 基于角色等级确定权限
    permissions = []
    
    if role_level >= 1:  # SPECIALIST
        permissions.extend([
            "read_own_projects",
            "edit_own_tasks",
            "view_assigned_documents"
        ])
    
    if role_level >= 2:  # MANAGER
        permissions.extend([
            "create_projects",
            "manage_team_tasks",
            "view_all_projects",
            "assign_tasks",
            "approve_content"
        ])
    
    if role_level >= 3:  # ADMIN
        permissions.extend([
            "manage_users",
            "system_configuration",
            "delete_projects",
            "view_system_logs",
            "manage_llm_configs"
        ])
    
    return {
        "role": user_role.value,
        "role_level": role_level,
        "permissions": permissions
    }


# 可选的认证依赖项，用于不强制要求认证的端点
def get_current_user_optional(
    db: Session = Depends(get_db),
    token: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[User]:
    """
    获取当前用户(可选)
    
    Args:
        db: 数据库会话
        token: JWT令牌(可选)
    
    Returns:
        用户对象或None
    """
    if token is None:
        return None
    
    username = verify_token(token.credentials)
    if username is None:
        return None
    
    user = UserService.get_user_by_username(db, username)
    if user is None or not user.is_active:
        return None
    
    return user


class RBACResponse:
    """RBAC响应类，用于API响应"""
    
    @staticmethod
    def permission_denied(required_roles: List[str], user_role: str = None):
        """权限拒绝响应"""
        detail = f"权限不足，需要以下角色之一: {', '.join(required_roles)}"
        if user_role:
            detail += f"，当前角色: {user_role}"
        
        return HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )
    
    @staticmethod
    def unauthorized():
        """未认证响应"""
        return HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未认证用户",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    @staticmethod
    def account_disabled():
        """账户被禁用响应"""
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被停用"
        ) 
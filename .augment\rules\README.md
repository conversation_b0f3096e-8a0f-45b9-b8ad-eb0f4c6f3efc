---
type: "always_apply"
---
# 智能标书生成系统 - Augment 规则总览

## 项目简介
这是一个由AI驱动的智能标书生成系统，旨在实现**投标响应速度提升80%，标书质量和一致性显著提高**的目标。

## 快速导航

### 📋 项目概述
- [项目概述](.augment/rules/project-overview.md) - 项目愿景、核心功能和技术栈
- [系统需求规则说明书.md](docs/系统需求规则说明书.md) - 完整的需求规格书

### 🏗️ 架构设计
- [架构指导原则](.augment/rules/architecture-guide.md) - 系统架构和设计原则
- [系统架构设计说明书.md](docs/系统架构设计说明书.md) - 详细的架构设计

### 💻 开发规范
- [开发标准](.augment/rules/development-standards.md) - 编码规范和开发流程
- [API设计规范](.augment/rules/api-design.md) - API接口设计和调用规范
- [RBAC开发指导原则](.augment/rules/rbac-development-guidelines.md) - **强制性权限控制规则**
- [开发任务清单.md](docs/开发任务清单.md) - 详细的开发任务和优先级

### 🗄️ 数据设计
- [数据库模式](.augment/rules/database-schema.md) - 核心数据模型和表结构
- [系统详细设计说明书.md](docs/系统详细设计说明书.md) - 详细的系统设计

### 🤖 AI集成
- [LLM集成指南](.augment/rules/llm-integration.md) - 多LLM支持和适配器模式

### 📝 前端开发
- [前端编辑器开发](.augment/rules/frontend-editor.md) - ProseMirror编辑器和AI集成

### 📊 任务管理
- [任务跟踪规则](.augment/rules/task-tracking.md) - 任务状态跟踪和更新规则（使用真实系统时间）
- [任务与更新说明.md](任务与更新说明.md) - 所有任务状态和更新记录

## 🔐 安全与权限控制

### RBAC强制性要求
**重要**: 所有新的API端点都必须实现适当的权限控制，这是强制性的安全要求。

#### 权限等级
- **Admin (管理员)**: 系统管理员，拥有所有权限
- **Manager (经理)**: 项目经理，可管理项目和分配任务
- **Specialist (专员)**: 技术专员，只能访问分配的任务

#### 权限层级
```
Admin (level 3) > Manager (level 2) > Specialist (level 1)
```

#### 测试用户
- **admin/admin123** - 管理员测试
- **manager/manager123** - 经理测试
- **specialist/specialist123** - 专员测试

#### 开发检查清单
在开发任何新的API端点时，必须检查：
- [ ] 是否导入了必要的RBAC模块？
- [ ] 是否在路由函数中添加了权限检查依赖？
- [ ] 是否选择了正确的权限级别？
- [ ] 是否在业务逻辑中进行了细粒度权限检查？
- [ ] 是否提供了清晰的错误信息？
- [ ] 是否编写了权限测试用例？
- [ ] 是否在API文档中标注了权限要求？

详细信息请参考：[RBAC开发指导原则](.augment/rules/rbac-development-guidelines.md)

## 核心工作流程

### 1. 智能解析
用户上传招标文档 → 文档解析引擎提取文本 → LLM分析生成结构化报告

### 2. 大纲生成
基于分析报告 → LLM生成投标书大纲 → 用户可编辑和调整

### 3. 内容生成
选择章节 → RAG知识库检索 → LLM生成章节内容 → ProseMirror编辑器展示

### 4. 智能优化
用户选中文本 → AI优化操作(润色/扩写/摘要) → 实时更新编辑器

### 5. 文档导出
ProseMirror文档JSON → 后端导出服务 → 生成PDF/DOCX文件

## 关键技术要点

### 分层架构
- **前端应用层**: React + ProseMirror编辑器
- **应用服务层**: FastAPI + LangChain工作流
- **核心引擎层**: 文档解析、LLM服务、导出服务
- **数据存储层**: PostgreSQL + pgvector + MinIO

### LLM适配器模式
```python
[前端/工作流] -> [LLM网关] -> [模型适配器] -> [具体LLM API]
```

### 数据安全
- JWT认证 + RBAC权限控制
- 敏感数据加密存储
- 私有化部署支持

### RBAC权限控制
```python
from app.core.rbac import require_admin, require_manager, require_specialist

@router.post("/admin-only")
async def admin_endpoint(current_user: User = Depends(require_admin())):
    # 仅管理员可访问
    pass
```

## 开发优先级
参考 [开发任务清单.md](docs/开发任务清单.md)：
- **P0**: 关键路径任务
- **P1**: MVP核心功能
- **P2**: 重要功能
- **P3**: 优化项

## 性能要求
- 文件解析(100页内)：30秒内完成
- 分析报告生成：2分钟内完成
- 章节内容生成：10秒内完成
- 文档导出(100页内)：1分钟内完成

## 用户角色
- **投标总监**: 决策和审核
- **项目经理**: 全程管理和协调
- **技术专家**: 技术方案撰写
- **商务专员**: 商务条款处理

---

**开发提示**: 在开发过程中，请始终参考相关的规则文件和文档，确保代码符合项目的架构设计和开发标准。**特别注意：所有新的API端点都必须实现RBAC权限控制！**
#!/usr/bin/env python3
"""
重新创建用户表并插入admin用户
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.core.config import get_settings
from app.core.security import get_password_hash

def recreate_users_table():
    """重新创建用户表并插入admin用户"""
    settings = get_settings()
    
    print(f"连接数据库: {settings.database_url}")
    
    try:
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        with engine.connect() as conn:
            # 删除现有用户表
            print("正在删除现有用户表...")
            conn.execute(text("DROP TABLE IF EXISTS users CASCADE"))
            conn.commit()
            
            # 创建新的用户表
            print("正在创建新的用户表...")
            conn.execute(text("""
                CREATE TABLE users (
                    id SERIAL PRIMARY KEY,
                    username VARCHAR(255) UNIQUE NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    hashed_password VARCHAR(255) NOT NULL,
                    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'manager', 'specialist')),
                    full_name VARCHAR(255),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    updated_at TIMESTAMPTZ DEFAULT NOW()
                )
            """))
            conn.commit()
            
            # 创建索引
            print("正在创建索引...")
            conn.execute(text("CREATE INDEX idx_users_username ON users(username)"))
            conn.execute(text("CREATE INDEX idx_users_email ON users(email)"))
            conn.execute(text("CREATE INDEX idx_users_role ON users(role)"))
            conn.execute(text("CREATE INDEX idx_users_is_active ON users(is_active)"))
            conn.commit()
            
            # 插入admin用户
            print("正在插入admin用户...")
            admin_password_hash = get_password_hash("admin123")
            conn.execute(text("""
                INSERT INTO users (username, email, hashed_password, role, full_name) 
                VALUES (:username, :email, :hashed_password, :role, :full_name)
            """), {
                "username": "admin",
                "email": "<EMAIL>",
                "hashed_password": admin_password_hash,
                "role": "admin",
                "full_name": "系统管理员"
            })
            conn.commit()
            
            # 验证插入结果
            result = conn.execute(text("SELECT id, username, email, role, full_name FROM users WHERE username = 'admin'"))
            admin_user = result.fetchone()
            
            if admin_user:
                print(f"✅ admin用户创建成功:")
                print(f"  - ID: {admin_user.id}")
                print(f"  - 用户名: {admin_user.username}")
                print(f"  - 邮箱: {admin_user.email}")
                print(f"  - 角色: {admin_user.role}")
                print(f"  - 姓名: {admin_user.full_name}")
                print(f"  - 密码: admin123")
            else:
                print("❌ admin用户创建失败")
                
    except Exception as e:
        print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    recreate_users_table() 
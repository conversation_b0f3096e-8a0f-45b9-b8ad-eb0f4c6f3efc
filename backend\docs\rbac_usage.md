# RBAC (基于角色的访问控制) 使用指南

## 概述

IntelliBid系统实现了完整的基于角色的访问控制(RBAC)系统，支持三种用户角色：

- **admin**: 系统管理员，拥有所有权限
- **manager**: 项目经理，可管理项目和分配任务
- **specialist**: 专员，只能访问分配给自己的任务

## 角色层级

系统采用层级权限模型：

```
admin (level 3) > manager (level 2) > specialist (level 1)
```

高级别角色自动拥有低级别角色的权限。

## 基本使用

### 1. 导入RBAC模块

```python
from app.core.rbac import (
    require_admin,
    require_manager,
    require_specialist,
    require_roles,
    require_any_role,
    get_current_user_from_token,
    check_user_permission,
    get_user_permissions
)
```

### 2. 在API端点中使用

#### 单一角色权限

```python
from fastapi import APIRouter, Depends
from app.core.rbac import require_admin, require_manager

router = APIRouter()

@router.get("/admin-only")
async def admin_endpoint(
    current_user: User = Depends(require_admin())
):
    return {"message": "只有管理员可以访问"}

@router.get("/manager-and-above")
async def manager_endpoint(
    current_user: User = Depends(require_manager())
):
    return {"message": "经理和管理员可以访问"}
```

#### 多角色权限

```python
from app.models.user import UserRole

@router.get("/admin-or-manager")
async def multi_role_endpoint(
    current_user: User = Depends(require_roles(UserRole.ADMIN, UserRole.MANAGER))
):
    return {"message": "管理员或经理可以访问"}
```

#### 精确角色匹配

```python
@router.get("/manager-only")
async def manager_only_endpoint(
    current_user: User = Depends(require_roles(UserRole.MANAGER, exact_match=True))
):
    return {"message": "只有经理可以访问，管理员不可以"}
```

### 3. 权限检查工具函数

#### 检查用户权限

```python
from app.core.rbac import check_user_permission

def some_business_logic(user: User):
    # 检查用户是否有管理员权限
    if check_user_permission(user, [UserRole.ADMIN]):
        # 执行管理员操作
        pass
    
    # 检查用户是否有经理或管理员权限
    if check_user_permission(user, [UserRole.MANAGER, UserRole.ADMIN]):
        # 执行经理操作
        pass
    
    # 精确角色匹配
    if check_user_permission(user, [UserRole.SPECIALIST], exact_match=True):
        # 只有专员可以执行
        pass
```

#### 获取用户权限信息

```python
from app.core.rbac import get_user_permissions

def get_user_info(user: User):
    permissions = get_user_permissions(user)
    return {
        "role": permissions["role"],
        "role_level": permissions["role_level"],
        "permissions": permissions["permissions"]
    }
```

## 权限装饰器

对于需要在函数级别进行权限控制的场景，可以使用权限装饰器：

```python
from app.core.rbac import permission_required

@permission_required(UserRole.ADMIN)
async def admin_function(current_user: User):
    # 只有管理员可以调用此函数
    pass

@permission_required(UserRole.MANAGER, UserRole.ADMIN)
async def manager_function(current_user: User):
    # 经理和管理员可以调用此函数
    pass

@permission_required(UserRole.SPECIALIST, exact_match=True)
async def specialist_only_function(current_user: User):
    # 只有专员可以调用此函数
    pass
```

## 错误处理

RBAC系统提供了标准的错误响应：

```python
from app.core.rbac import RBACResponse

# 权限不足
raise RBACResponse.permission_denied(["admin", "manager"], "specialist")

# 未认证
raise RBACResponse.unauthorized()

# 账户被禁用
raise RBACResponse.account_disabled()
```

## 测试API端点

系统提供了完整的RBAC测试端点，可以用于验证权限控制：

### 基本权限测试

```bash
# 获取当前用户信息
GET /api/v1/rbac/me

# 获取用户权限
GET /api/v1/rbac/me/permissions

# 管理员端点
GET /api/v1/rbac/admin-only

# 经理端点
GET /api/v1/rbac/manager-and-above

# 专员端点
GET /api/v1/rbac/specialist-and-above
```

### 权限检查端点

```bash
# 检查用户权限
POST /api/v1/rbac/check-permission
{
    "required_roles": ["admin", "manager"],
    "exact_match": false
}
```

### 系统信息端点

```bash
# 获取系统信息 (仅管理员)
GET /api/v1/rbac/system-info
```

## 实际应用示例

### 项目管理API

```python
from fastapi import APIRouter, Depends
from app.core.rbac import require_manager, require_any_role, check_user_permission

router = APIRouter()

@router.post("/projects")
async def create_project(
    project_data: ProjectCreate,
    current_user: User = Depends(require_manager())
):
    """创建项目 - 需要经理权限"""
    # 经理和管理员可以创建项目
    pass

@router.get("/projects")
async def list_projects(
    current_user: User = Depends(require_any_role())
):
    """获取项目列表 - 任何角色都可以"""
    # 根据用户角色返回不同的项目列表
    if check_user_permission(current_user, [UserRole.ADMIN]):
        # 管理员可以看到所有项目
        projects = get_all_projects()
    elif check_user_permission(current_user, [UserRole.MANAGER]):
        # 经理可以看到自己管理的项目
        projects = get_projects_by_manager(current_user.id)
    else:
        # 专员只能看到分配给自己的项目
        projects = get_projects_by_specialist(current_user.id)
    
    return projects

@router.delete("/projects/{project_id}")
async def delete_project(
    project_id: int,
    current_user: User = Depends(require_admin())
):
    """删除项目 - 仅管理员可以"""
    # 只有管理员可以删除项目
    pass
```

## 最佳实践

1. **使用依赖注入**: 优先使用FastAPI的依赖注入来进行权限检查
2. **细粒度权限控制**: 根据业务需求选择合适的权限级别
3. **错误处理**: 提供清晰的错误信息，告知用户所需的权限
4. **测试覆盖**: 为每个权限级别编写测试用例
5. **文档化**: 在API文档中明确标注所需权限

## 扩展

如果需要添加新的角色或权限，可以：

1. 在`UserRole`枚举中添加新角色
2. 更新`RBACMiddleware`中的角色层级
3. 在`get_user_permissions`中添加新角色的权限
4. 创建对应的权限检查函数

## 测试

运行RBAC功能测试：

```bash
cd backend
python scripts/test_rbac_functionality.py
```

运行单元测试：

```bash
cd backend
python -m pytest tests/test_rbac.py -v
``` 
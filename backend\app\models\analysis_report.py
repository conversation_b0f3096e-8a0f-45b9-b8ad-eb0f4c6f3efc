"""
分析报告模型
定义分析报告表的SQLAlchemy模型
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, CheckConstraint, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB

from ..database import Base


class GenerationStatus(str, Enum):
    """报告生成状态枚举"""
    PENDING = "pending"        # 待生成
    PROCESSING = "processing"  # 生成中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 生成失败


class AnalysisReport(Base):
    """分析报告模型"""
    __tablename__ = "analysis_reports"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), unique=True, nullable=False, index=True)
    tender_document_id = Column(Integer, ForeignKey("tender_documents.id"), nullable=False, index=True)
    report_content = Column(JSONB, nullable=False)
    generation_status = Column(String(50), nullable=False, default=GenerationStatus.PENDING.value, index=True)
    llm_model_used = Column(String(255), nullable=True)
    token_usage = Column(JSONB, nullable=True)
    generated_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    generated_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 添加约束
    __table_args__ = (
        CheckConstraint(
            generation_status.in_(['pending', 'processing', 'completed', 'failed']),
            name='check_analysis_report_generation_status'
        ),
    )

    # 关系定义
    project = relationship("Project", back_populates="analysis_report")
    tender_document = relationship("TenderDocument", back_populates="analysis_reports")
    generated_by = relationship("User", foreign_keys=[generated_by_user_id])

    def __repr__(self):
        return f"<AnalysisReport(id={self.id}, project_id={self.project_id}, status='{self.generation_status}')>"

    def dict(self):
        """转换为字典格式，用于API响应"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "tender_document_id": self.tender_document_id,
            "report_content": self.report_content,
            "generation_status": self.generation_status,
            "llm_model_used": self.llm_model_used,
            "token_usage": self.token_usage,
            "generated_by_user_id": self.generated_by_user_id,
            "generated_at": self.generated_at.isoformat() if self.generated_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def can_be_accessed_by(self, user) -> bool:
        """检查用户是否可以访问此分析报告"""
        from .user import UserRole
        
        # 管理员可以访问所有报告
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 通过项目权限检查
        if hasattr(self, 'project') and self.project:
            return self.project.can_be_accessed_by(user)
        
        return False

    def can_be_edited_by(self, user) -> bool:
        """检查用户是否可以编辑此分析报告"""
        from .user import UserRole
        
        # 管理员可以编辑所有报告
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 通过项目权限检查
        if hasattr(self, 'project') and self.project:
            return self.project.can_be_edited_by(user)
        
        return False

    def can_be_deleted_by(self, user) -> bool:
        """检查用户是否可以删除此分析报告"""
        from .user import UserRole
        
        # 只有管理员可以删除分析报告
        return user.role == UserRole.ADMIN.value

    def is_generation_completed(self) -> bool:
        """检查报告生成是否完成"""
        return self.generation_status == GenerationStatus.COMPLETED.value

    def is_generation_failed(self) -> bool:
        """检查报告生成是否失败"""
        return self.generation_status == GenerationStatus.FAILED.value

    def get_project_info(self) -> dict:
        """获取项目基本信息"""
        if self.report_content and isinstance(self.report_content, dict):
            return self.report_content.get('project_info', {})
        return {}

    def get_requirements(self) -> dict:
        """获取需求信息"""
        if self.report_content and isinstance(self.report_content, dict):
            return self.report_content.get('requirements', {})
        return {}

    def get_scoring_criteria(self) -> dict:
        """获取评分标准"""
        if self.report_content and isinstance(self.report_content, dict):
            return self.report_content.get('scoring_criteria', {})
        return {}

    def get_risk_analysis(self) -> dict:
        """获取风险分析"""
        if self.report_content and isinstance(self.report_content, dict):
            return self.report_content.get('risk_analysis', {})
        return {}

    def get_recommendations(self) -> dict:
        """获取建议策略"""
        if self.report_content and isinstance(self.report_content, dict):
            return self.report_content.get('recommendations', {})
        return {}

    def get_token_cost(self) -> float:
        """获取Token成本"""
        if self.token_usage and isinstance(self.token_usage, dict):
            input_tokens = self.token_usage.get('input_tokens', 0)
            output_tokens = self.token_usage.get('output_tokens', 0)
            # 这里可以根据不同模型计算成本
            return (input_tokens * 0.0001 + output_tokens * 0.0002)  # 示例价格
        return 0.0

    def update_generation_status(self, status: GenerationStatus, report_content: dict = None):
        """更新生成状态"""
        self.generation_status = status.value
        if status == GenerationStatus.COMPLETED and report_content:
            self.report_content = report_content
        elif status == GenerationStatus.FAILED:
            # 保留之前的内容，但标记为失败
            pass

    def add_token_usage(self, input_tokens: int, output_tokens: int, model_name: str):
        """添加Token使用记录"""
        self.token_usage = {
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'total_tokens': input_tokens + output_tokens,
            'model_name': model_name,
            'timestamp': datetime.utcnow().isoformat()
        }
        self.llm_model_used = model_name 
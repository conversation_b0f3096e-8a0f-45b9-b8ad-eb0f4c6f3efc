### **智能标书生成系统：系统详细设计说明书 (DDD)**

**版本**: 1.0 **关联文档**: URS V2.0, 系统架构设计说明书 V1.0

#### **1. 引言**

本文档旨在对系统架构设计进行深化和细化，定义核心模块的职责、接口协议（API）、数据实体关系以及关键工作流的交互时序。本文档是连接架构蓝图与开发实现的桥梁。

#### **2. 核心领域模型 (Core Domain Models)**

系统围绕以下核心业务对象进行设计：

- **User**: 系统用户，拥有不同角色（`admin`, `manager`, `specialist`）。
- **Project**: 一次完整的投标活动，作为所有相关文档和活动的容器。
- **TenderDocument**: 用户上传的原始招标文档。
- **AnalysisReport**: 由AI生成的结构化分析报告。
- **BidDocument**: 用户在ProseMirror编辑器中创作的投标书，以结构化JSON形式存储。
- **KnowledgeItem**: 知识库中的原子单元，通常是文档的一个切片（Chunk）。
- **LLMConfig**: 管理员配置的LLM引擎信息。

#### **3. 模块/服务详细设计**

##### **3.1. 工作流编排服务 (Workflow Orchestration Service)**

- **模块职责**:
  - 作为业务逻辑的核心协调者，管理投标项目的生命周期状态（`analyzing`, `outlining`, `drafting`, `completed`）。
  - 实现核心的LangChain链，按顺序调用下游服务（解析、RAG、LLM、导出）。
  - 处理与项目、分析报告、标书内容相关的业务操作。
- **核心API接口设计**:
  - `POST /api/v1/projects`: 创建一个新的投标项目。
  - `POST /api/v1/projects/{projectId}/tender-document`: 上传招标文档，并触发后台解析工作流。
  - `GET /api/v1/projects/{projectId}/analysis-report`: 获取指定项目的结构化分析报告。
  - `POST /api/v1/projects/{projectId}/generate-outline`: 基于分析报告生成投标书大纲。
  - `PUT /api/v1/projects/{projectId}/bid-document`: 保存/更新ProseMirror编辑器的投标书内容（JSON格式）。
  - `POST /api/v1/projects/{projectId}/generate-chapter`: **【核心】** 为指定章节生成内容。
    - **Request Body**: `{ "chapterId": "3.5.1", "llmModelId": "deepseek-v2" }`
    - **Response**: `Streaming` 或 `JSON` 形式的生成内容。
  - `POST /api/v1/ai/optimize-text`: **【核心】** 执行编辑器内的AI优化操作。
    - **Request Body**: `{ "text": "...", "action": "polish" | "expand" | "summarize", "llmModelId": "gpt-4o" }`

##### **3.2. LLM服务层 (LLM Service Layer)**

- **模块职责**:
  - 抽象并统一对不同LLM的调用。
  - 根据传入的`model_id`，动态选择对应的适配器。
  - 处理认证、API格式转换等细节。
- **核心API接口设计 (内部接口)**:
  - `POST /internal/v1/llm/chat/completions`: 统一的聊天补全接口（兼容OpenAI API格式）。
    - **Request Body**: `{ "model": "llmModelId", "messages": [{"role": "user", "content": "..."}], "stream": false }`
    - **Response**: 同样兼容OpenAI API的响应格式。

##### **3.3. 知识库管理与检索服务 (Knowledge Base Service)**

- **模块职责**:
  - 提供文档的入库（Ingestion）功能，包括加载、清洗、分块、向量化和存储。
  - 提供高效的向量检索（RAG）功能，支持元数据过滤。
- **核心API接口设计 (内部接口)**:
  - `POST /internal/v1/kb/ingest`: 触发对一个已上传文档的知识库入库流程。
    - **Request Body**: `{ "sourceDocumentPath": "/path/to/doc", "metadata": {"project_type": "hospital"} }`
  - `POST /internal/v1/kb/search`: 执行向量相似度搜索。
    - **Request Body**: `{ "queryText": "...", "topK": 5, "filters": {"project_type": "hospital"} }`
    - **Response**: `{ "results": [{"content": "...", "metadata": {...}, "score": 0.9}] }`

##### **3.4. 文档导出服务 (Document Export Service)**

- **模块职责**:
  - 接收结构化的ProseMirror文档内容（JSON格式）。
  - 在服务端进行高保真的格式转换。
- **核心API接口设计 (内部接口)**:
  - `POST /internal/v1/export`: 执行导出任务。
    - **Request Body**: `{ "proseMirrorJson": {...}, "format": "docx" | "pdf" }`
    - **Response**: `{ "downloadUrl": "...", "status": "completed" }` 或异步任务ID。

------

#### **4. 数据库详细设计 (PostgreSQL + pgvector)**

##### **4.1. 逻辑ER图**

```
[Users] 1--* [Projects] 1--1 [TenderDocument] 1--1 [AnalysisReport]
  |                                        |
  +--* [KnowledgeItems]                    +--1 [BidDocument]

[LLMConfigs] (独立配置表)
```

##### **4.2. 表结构定义 (SQL DDL)**

SQL

```
-- 启用pgvector扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL, -- 'admin', 'manager', 'specialist'
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 项目表
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'new',
    created_by_user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 原始招标文档表
CREATE TABLE tender_documents (
    id SERIAL PRIMARY KEY,
    project_id INTEGER UNIQUE REFERENCES projects(id),
    original_filename VARCHAR(255) NOT NULL,
    storage_path VARCHAR(1024) NOT NULL, -- 对象存储中的路径
    extracted_text_path VARCHAR(1024), -- 提取后的纯文本路径
    uploaded_at TIMESTAMPTZ DEFAULT NOW()
);

-- 投标书内容表
CREATE TABLE bid_documents (
    id SERIAL PRIMARY KEY,
    project_id INTEGER UNIQUE REFERENCES projects(id),
    prosemirror_json JSONB NOT NULL, -- 存储ProseMirror的文档结构
    version INTEGER NOT NULL DEFAULT 1,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 知识库条目表 (包含向量)
CREATE TABLE knowledge_items (
    id SERIAL PRIMARY KEY,
    content_chunk TEXT NOT NULL,
    metadata JSONB, -- 存储源文件、项目类型等元数据
    embedding VECTOR(1536) NOT NULL, -- 假设使用1536维的向量
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 为向量列创建HNSW索引以加速查询
CREATE INDEX ON knowledge_items USING hnsw (embedding vector_cosine_ops);

-- LLM配置表
CREATE TABLE llm_configs (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(255) UNIQUE NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- 'online', 'private'
    api_endpoint VARCHAR(1024) NOT NULL,
    api_key_secret_name VARCHAR(255), -- 存储在KMS或Vault中的密钥名称，而非明文
    is_default BOOLEAN DEFAULT FALSE
);
```

------

#### **5. 关键工作流时序图**

**工作流: 智能解析与分析报告生成**

1. **User** -> **Frontend**: 上传`招标书.docx`文件。
2. **Frontend** -> **API Gateway**: `POST /api/v1/projects/{id}/tender-document` 请求，携带文件。
3. **API Gateway** -> **Workflow Service**: 转发请求。
4. **Workflow Service** -> **Object Storage**: 将上传的`.docx`文件保存，获取`storage_path`。
5. **Workflow Service** -> **PostgreSQL**: 更新`tender_documents`表，记录`storage_path`。
6. **Workflow Service** -> **Parsing Engine**: `POST /internal/v1/parse-document`，请求体为`{"source_path": "..."}`。
7. **Parsing Engine**: 读取文件，提取纯文本，将文本保存到**Object Storage**，返回`text_path`。
8. **Workflow Service**: 接收到`text_path`，从**Object Storage**读取纯文本内容。
9. **Workflow Service**: 构造`Prompt_Step1_Analysis`，将纯文本内容作为输入。
10. **Workflow Service** -> **LLM Service Layer**: `POST /internal/v1/llm/chat/completions`，调用默认LLM进行分析。
11. **LLM Service Layer**: 路由到对应的LLM Adapter，获取结构化的分析报告（Markdown格式）。
12. **Workflow Service**: 接收到分析报告。
13. **Workflow Service** -> **PostgreSQL**: 将分析报告内容保存到`analysis_reports`表中（为简化，本设计中可直接存为`projects`表的一个`JSONB`字段）。
14. **Workflow Service** -> **Frontend**: 返回成功响应，`{"message": "Analysis completed."}`。
15. **Frontend**: 收到成功响应后，通过`GET /api/v1/projects/{id}/analysis-report`请求，获取并展示报告。

------

#### **6. 非功能性设计实现**

- **安全性 (Security)**:
  - 所有面向前端的API都将通过**JWT (JSON Web Token)**进行用户认证。
  - API网关将实现基于用户角色的访问控制（RBAC）。
  - 数据库密码、LLM API Key等敏感凭证将存储在专用的密钥管理服务中（如HashiCorp Vault或云厂商的KMS），应用在运行时动态获取。
- **可扩展性 (Scalability)**:
  - 所有后端服务均为**无状态**设计，可以通过在Kubernetes中增加Pod副本数量来进行水平扩展。
  - PostgreSQL可通过**读写分离**和**Read Replicas**来扩展读性能。
  - 文档解析、导出等CPU/IO密集型服务，以及私有LLM等GPU密集型服务，可以部署在独立的、可按需伸缩的节点组上。
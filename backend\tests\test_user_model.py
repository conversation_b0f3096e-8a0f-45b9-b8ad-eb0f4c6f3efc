"""
用户模型测试
测试用户模型的基本功能和权限检查
"""

import pytest
from app.models.user import User, UserRole


def test_user_model_creation():
    """测试用户模型创建"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password_123",
        role=UserRole.MANAGER.value,
        full_name="Test User"
    )
    
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert user.role == "manager"
    assert user.full_name == "Test User"
    assert user.is_active is True  # 默认值


def test_user_role_enum():
    """测试用户角色枚举"""
    assert UserRole.ADMIN.value == "admin"
    assert UserRole.MANAGER.value == "manager"
    assert UserRole.SPECIALIST.value == "specialist"


def test_user_permission_hierarchy():
    """测试用户权限层级"""
    admin = User(
        username="admin",
        email="<EMAIL>", 
        hashed_password="hash",
        role=UserRole.ADMIN.value
    )
    
    manager = User(
        username="manager",
        email="<EMAIL>",
        hashed_password="hash", 
        role=UserRole.MANAGER.value
    )
    
    specialist = User(
        username="specialist",
        email="<EMAIL>",
        hashed_password="hash",
        role=UserRole.SPECIALIST.value
    )
    
    # 管理员拥有所有权限
    assert admin.has_permission(UserRole.ADMIN) is True
    assert admin.has_permission(UserRole.MANAGER) is True
    assert admin.has_permission(UserRole.SPECIALIST) is True
    
    # 经理拥有经理和专员权限
    assert manager.has_permission(UserRole.ADMIN) is False
    assert manager.has_permission(UserRole.MANAGER) is True
    assert manager.has_permission(UserRole.SPECIALIST) is True
    
    # 专员只有专员权限
    assert specialist.has_permission(UserRole.ADMIN) is False
    assert specialist.has_permission(UserRole.MANAGER) is False
    assert specialist.has_permission(UserRole.SPECIALIST) is True


def test_user_dict_conversion():
    """测试用户模型转字典"""
    user = User(
        id=1,
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password_123",
        role=UserRole.MANAGER.value,
        full_name="Test User"
    )
    
    user_dict = user.dict()
    
    assert user_dict["id"] == 1
    assert user_dict["username"] == "testuser"
    assert user_dict["email"] == "<EMAIL>"
    assert user_dict["role"] == "manager"
    assert user_dict["full_name"] == "Test User"
    assert user_dict["is_active"] is True
    # 密码不应该在字典中暴露
    assert "hashed_password" not in user_dict


def test_user_repr():
    """测试用户模型字符串表示"""
    user = User(
        id=1,
        username="testuser",
        role=UserRole.MANAGER.value
    )
    
    repr_str = repr(user)
    assert "User(id=1" in repr_str
    assert "username='testuser'" in repr_str
    assert "role='manager'" in repr_str


# --- Suggested Test ---
# pytest backend/tests/test_user_model.py -v 
-- Migration: 001_create_users_table.sql
-- Description: 创建用户表，包含角色字段
-- Created: 2025-07-14T16:27:42+08:00

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'manager', 'specialist')),
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);

-- 创建更新时间的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为用户表添加更新时间触发器
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 插入默认管理员用户 (密码: admin123，实际使用时需要哈希)
-- WHY: 提供系统初始化时的默认管理员账户
INSERT INTO users (username, email, hashed_password, role, full_name) VALUES 
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtO5/XlAoq', 'admin', '系统管理员');

-- 添加注释
COMMENT ON TABLE users IS '系统用户表，存储用户认证和角色信息';
COMMENT ON COLUMN users.id IS '用户唯一标识符';
COMMENT ON COLUMN users.username IS '用户名，用于登录';
COMMENT ON COLUMN users.email IS '用户邮箱';
COMMENT ON COLUMN users.hashed_password IS '哈希后的密码';
COMMENT ON COLUMN users.role IS '用户角色: admin(管理员), manager(项目经理), specialist(专员)';
COMMENT ON COLUMN users.full_name IS '用户真实姓名';
COMMENT ON COLUMN users.is_active IS '用户是否激活';
COMMENT ON COLUMN users.created_at IS '创建时间';
COMMENT ON COLUMN users.updated_at IS '最后更新时间'; 
---
type: "always_apply"
---
# 数据库模式设计

## 核心领域模型
基于 [系统详细设计说明书.md](docs/系统详细设计说明书.md) 中的领域模型设计：

- **User**: 系统用户，拥有不同角色(admin, manager, specialist)
- **Project**: 一次完整的投标活动，作为所有相关文档和活动的容器
- **TenderDocument**: 用户上传的原始招标文档
- **AnalysisReport**: 由AI生成的结构化分析报告
- **BidDocument**: 用户在ProseMirror编辑器中创作的投标书
- **KnowledgeItem**: 知识库中的原子单元，通常是文档的一个切片
- **LLMConfig**: 管理员配置的LLM引擎信息

## 数据库表结构

### 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL, -- 'admin', 'manager', 'specialist'
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 项目表 (projects)
```sql
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'new', -- 'new', 'analyzing', 'outlining', 'drafting', 'completed'
    created_by_user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 招标文档表 (tender_documents)
```sql
CREATE TABLE tender_documents (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(1024) NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(255),
    extracted_text TEXT,
    uploaded_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 分析报告表 (analysis_reports)
```sql
CREATE TABLE analysis_reports (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    content JSONB NOT NULL,
    generated_at TIMESTAMPTZ DEFAULT NOW(),
    llm_model VARCHAR(255)
);
```

### 投标书表 (bid_documents)
```sql
CREATE TABLE bid_documents (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content JSONB NOT NULL, -- ProseMirror文档JSON
    outline JSONB, -- 大纲结构
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 知识库表 (knowledge_items)
```sql
CREATE TABLE knowledge_items (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    embedding VECTOR(1536), -- 向量嵌入，用于相似度搜索
    metadata JSONB, -- 标签、分类等元数据
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### LLM配置表 (llm_configs)
```sql
CREATE TABLE llm_configs (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(255) UNIQUE NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- 'online', 'private'
    api_endpoint VARCHAR(1024) NOT NULL,
    api_key_secret_name VARCHAR(255),
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 索引策略

### 性能优化索引
- `users(username)` - 用户登录查询
- `projects(created_by_user_id)` - 用户项目列表
- `projects(status)` - 项目状态筛选
- `knowledge_items(embedding)` - 向量相似度搜索(HNSW)

### 复合索引
- `projects(created_by_user_id, status)` - 用户特定状态项目
- `knowledge_items(metadata)` - 知识库元数据筛选(GIN索引)

## 数据完整性约束

### 外键约束
- 所有外键必须明确定义，确保数据一致性
- 使用级联删除策略处理关联数据

### 检查约束
- 用户角色限制：`role IN ('admin', 'manager', 'specialist')`
- 项目状态限制：`status IN ('new', 'analyzing', 'outlining', 'drafting', 'completed')`
- LLM类型限制：`model_type IN ('online', 'private')`

## 数据安全

### 敏感数据处理
- 密码使用bcrypt哈希存储
- API密钥存储在专用密钥管理服务中
- 用户上传的文档内容加密存储

### 访问控制
- 基于用户角色的行级安全(RLS)
- 审计日志记录所有数据变更
- 定期备份和恢复策略
"""
RBAC (基于角色的访问控制) 测试
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.database import get_db
from app.models.user import User, UserRole
from app.core.rbac import (
    rbac,
    check_user_permission,
    get_user_permissions,
    require_roles,
    RBACResponse
)
from app.services.user_service import UserService
from app.core.security import create_access_token

client = TestClient(app)


class TestRBACMiddleware:
    """测试RBAC中间件类"""
    
    def test_role_hierarchy(self):
        """测试角色层级"""
        assert rbac.get_role_level(UserRole.ADMIN) == 3
        assert rbac.get_role_level(UserRole.MANAGER) == 2
        assert rbac.get_role_level(UserRole.SPECIALIST) == 1
    
    def test_check_permission_hierarchy(self):
        """测试权限层级检查"""
        # 管理员可以访问所有角色的权限
        assert rbac.check_permission(UserRole.ADMIN, [UserRole.ADMIN])
        assert rbac.check_permission(UserRole.ADMIN, [UserRole.MANAGER])
        assert rbac.check_permission(UserRole.ADMIN, [UserRole.SPECIALIST])
        
        # 经理可以访问经理和专员权限
        assert rbac.check_permission(UserRole.MANAGER, [UserRole.MANAGER])
        assert rbac.check_permission(UserRole.MANAGER, [UserRole.SPECIALIST])
        assert not rbac.check_permission(UserRole.MANAGER, [UserRole.ADMIN])
        
        # 专员只能访问专员权限
        assert rbac.check_permission(UserRole.SPECIALIST, [UserRole.SPECIALIST])
        assert not rbac.check_permission(UserRole.SPECIALIST, [UserRole.MANAGER])
        assert not rbac.check_permission(UserRole.SPECIALIST, [UserRole.ADMIN])
    
    def test_check_exact_roles(self):
        """测试精确角色匹配"""
        # 精确匹配不考虑层级
        assert rbac.check_exact_roles(UserRole.ADMIN, [UserRole.ADMIN])
        assert not rbac.check_exact_roles(UserRole.ADMIN, [UserRole.MANAGER])
        assert not rbac.check_exact_roles(UserRole.ADMIN, [UserRole.SPECIALIST])
        
        assert rbac.check_exact_roles(UserRole.MANAGER, [UserRole.MANAGER])
        assert not rbac.check_exact_roles(UserRole.MANAGER, [UserRole.ADMIN])
        assert not rbac.check_exact_roles(UserRole.MANAGER, [UserRole.SPECIALIST])
    
    def test_check_permission_multiple_roles(self):
        """测试多角色权限检查"""
        # 管理员或经理可以访问
        assert rbac.check_permission(UserRole.ADMIN, [UserRole.ADMIN, UserRole.MANAGER])
        assert rbac.check_permission(UserRole.MANAGER, [UserRole.ADMIN, UserRole.MANAGER])
        assert not rbac.check_permission(UserRole.SPECIALIST, [UserRole.ADMIN, UserRole.MANAGER])


class TestRBACUtils:
    """测试RBAC工具函数"""
    
    def test_check_user_permission(self, db: Session):
        """测试用户权限检查工具函数"""
        # 创建测试用户
        admin_user = User(
            username="admin_test",
            email="<EMAIL>",
            hashed_password="hashed",
            role=UserRole.ADMIN.value,
            is_active=True
        )
        
        manager_user = User(
            username="manager_test",
            email="<EMAIL>",
            hashed_password="hashed",
            role=UserRole.MANAGER.value,
            is_active=True
        )
        
        specialist_user = User(
            username="specialist_test",
            email="<EMAIL>",
            hashed_password="hashed",
            role=UserRole.SPECIALIST.value,
            is_active=True
        )
        
        # 测试管理员权限
        assert check_user_permission(admin_user, [UserRole.ADMIN])
        assert check_user_permission(admin_user, [UserRole.MANAGER])
        assert check_user_permission(admin_user, [UserRole.SPECIALIST])
        
        # 测试经理权限
        assert not check_user_permission(manager_user, [UserRole.ADMIN])
        assert check_user_permission(manager_user, [UserRole.MANAGER])
        assert check_user_permission(manager_user, [UserRole.SPECIALIST])
        
        # 测试专员权限
        assert not check_user_permission(specialist_user, [UserRole.ADMIN])
        assert not check_user_permission(specialist_user, [UserRole.MANAGER])
        assert check_user_permission(specialist_user, [UserRole.SPECIALIST])
        
        # 测试精确匹配
        assert not check_user_permission(admin_user, [UserRole.MANAGER], exact_match=True)
        assert check_user_permission(manager_user, [UserRole.MANAGER], exact_match=True)
        assert not check_user_permission(manager_user, [UserRole.ADMIN], exact_match=True)
    
    def test_get_user_permissions(self):
        """测试获取用户权限信息"""
        # 创建测试用户
        admin_user = User(
            username="admin_test",
            email="<EMAIL>",
            hashed_password="hashed",
            role=UserRole.ADMIN.value,
            is_active=True
        )
        
        manager_user = User(
            username="manager_test",
            email="<EMAIL>",
            hashed_password="hashed",
            role=UserRole.MANAGER.value,
            is_active=True
        )
        
        specialist_user = User(
            username="specialist_test",
            email="<EMAIL>",
            hashed_password="hashed",
            role=UserRole.SPECIALIST.value,
            is_active=True
        )
        
        # 测试管理员权限
        admin_perms = get_user_permissions(admin_user)
        assert admin_perms["role"] == "admin"
        assert admin_perms["role_level"] == 3
        assert "manage_users" in admin_perms["permissions"]
        assert "create_projects" in admin_perms["permissions"]
        assert "read_own_projects" in admin_perms["permissions"]
        
        # 测试经理权限
        manager_perms = get_user_permissions(manager_user)
        assert manager_perms["role"] == "manager"
        assert manager_perms["role_level"] == 2
        assert "manage_users" not in manager_perms["permissions"]
        assert "create_projects" in manager_perms["permissions"]
        assert "read_own_projects" in manager_perms["permissions"]
        
        # 测试专员权限
        specialist_perms = get_user_permissions(specialist_user)
        assert specialist_perms["role"] == "specialist"
        assert specialist_perms["role_level"] == 1
        assert "manage_users" not in specialist_perms["permissions"]
        assert "create_projects" not in specialist_perms["permissions"]
        assert "read_own_projects" in specialist_perms["permissions"]
    
    def test_inactive_user_permissions(self):
        """测试非活跃用户权限"""
        inactive_user = User(
            username="inactive_test",
            email="<EMAIL>",
            hashed_password="hashed",
            role=UserRole.ADMIN.value,
            is_active=False
        )
        
        # 非活跃用户没有权限
        assert not check_user_permission(inactive_user, [UserRole.ADMIN])
        assert not check_user_permission(inactive_user, [UserRole.MANAGER])
        assert not check_user_permission(inactive_user, [UserRole.SPECIALIST])
        
        # 非活跃用户权限信息为空
        perms = get_user_permissions(inactive_user)
        assert perms["permissions"] == []
        assert perms["role_level"] == 0


class TestRBACResponse:
    """测试RBAC响应类"""
    
    def test_permission_denied(self):
        """测试权限拒绝响应"""
        response = RBACResponse.permission_denied(["admin", "manager"])
        assert response.status_code == 403
        assert "权限不足" in response.detail
        assert "admin" in response.detail
        assert "manager" in response.detail
        
        # 带用户角色的响应
        response_with_role = RBACResponse.permission_denied(["admin"], "specialist")
        assert "当前角色: specialist" in response_with_role.detail
    
    def test_unauthorized(self):
        """测试未认证响应"""
        response = RBACResponse.unauthorized()
        assert response.status_code == 401
        assert "未认证用户" in response.detail
        assert "Bearer" in response.headers["WWW-Authenticate"]
    
    def test_account_disabled(self):
        """测试账户被禁用响应"""
        response = RBACResponse.account_disabled()
        assert response.status_code == 400
        assert "用户账户已被停用" in response.detail


# --- Suggested Test ---
def test_rbac_middleware_integration():
    """
    集成测试：验证RBAC中间件在实际API中的工作
    """
    # 创建测试用户和令牌
    admin_token = create_access_token("admin_user")
    manager_token = create_access_token("manager_user")
    specialist_token = create_access_token("specialist_user")
    
    # 测试管理员访问
    response = client.get(
        "/api/v1/rbac/admin-only",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    # 注意：这个测试可能需要实际的用户数据库记录才能通过
    # 在实际环境中，需要先创建对应的用户记录
    
    # 测试未认证访问
    response = client.get("/api/v1/rbac/admin-only")
    assert response.status_code == 401 
#!/usr/bin/env python3
"""
RBAC功能测试脚本
验证基于角色的访问控制是否正常工作
"""

import sys
import os
import requests
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.security import create_access_token
from app.models.user import UserRole

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def print_section(title):
    """打印分节标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_test_result(test_name, success, details=""):
    """打印测试结果"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"   {details}")

def test_rbac_endpoints():
    """测试RBAC端点"""
    print_section("RBAC端点测试")
    
    # 创建不同角色的测试令牌
    admin_token = create_access_token("admin")
    manager_token = create_access_token("manager")
    specialist_token = create_access_token("specialist")
    
    print(f"生成的测试令牌:")
    print(f"  Admin Token: {admin_token[:50]}...")
    print(f"  Manager Token: {manager_token[:50]}...")
    print(f"  Specialist Token: {specialist_token[:50]}...")
    
    # 测试用例
    test_cases = [
        {
            "name": "管理员访问管理员端点",
            "url": f"{BASE_URL}/rbac/admin-only",
            "token": admin_token,
            "expected_status": 200,
            "should_pass": True
        },
        {
            "name": "经理访问管理员端点",
            "url": f"{BASE_URL}/rbac/admin-only",
            "token": manager_token,
            "expected_status": 403,
            "should_pass": False
        },
        {
            "name": "专员访问管理员端点",
            "url": f"{BASE_URL}/rbac/admin-only",
            "token": specialist_token,
            "expected_status": 403,
            "should_pass": False
        },
        {
            "name": "管理员访问经理端点",
            "url": f"{BASE_URL}/rbac/manager-and-above",
            "token": admin_token,
            "expected_status": 200,
            "should_pass": True
        },
        {
            "name": "经理访问经理端点",
            "url": f"{BASE_URL}/rbac/manager-and-above",
            "token": manager_token,
            "expected_status": 200,
            "should_pass": True
        },
        {
            "name": "专员访问经理端点",
            "url": f"{BASE_URL}/rbac/manager-and-above",
            "token": specialist_token,
            "expected_status": 403,
            "should_pass": False
        },
        {
            "name": "所有角色访问专员端点",
            "url": f"{BASE_URL}/rbac/specialist-and-above",
            "token": admin_token,
            "expected_status": 200,
            "should_pass": True
        },
        {
            "name": "未认证访问",
            "url": f"{BASE_URL}/rbac/admin-only",
            "token": None,
            "expected_status": 401,
            "should_pass": False
        }
    ]
    
    # 执行测试
    for test_case in test_cases:
        try:
            headers = {}
            if test_case["token"]:
                headers["Authorization"] = f"Bearer {test_case['token']}"
            
            response = requests.get(test_case["url"], headers=headers, timeout=10)
            
            success = response.status_code == test_case["expected_status"]
            details = f"状态码: {response.status_code}"
            
            if success and test_case["should_pass"] and response.status_code == 200:
                try:
                    data = response.json()
                    if data.get("code") == 200:
                        details += f" | 消息: {data.get('data', {}).get('message', 'N/A')}"
                except:
                    pass
            elif not success:
                details += f" | 期望: {test_case['expected_status']}"
                if response.status_code in [401, 403]:
                    try:
                        error_data = response.json()
                        details += f" | 错误: {error_data.get('detail', 'N/A')}"
                    except:
                        pass
            
            print_test_result(test_case["name"], success, details)
            
        except requests.exceptions.RequestException as e:
            print_test_result(test_case["name"], False, f"请求失败: {str(e)}")
        except Exception as e:
            print_test_result(test_case["name"], False, f"未知错误: {str(e)}")

def test_permission_check_endpoint():
    """测试权限检查端点"""
    print_section("权限检查端点测试")
    
    admin_token = create_access_token("admin")
    
    test_cases = [
        {
            "name": "检查管理员权限",
            "data": {
                "required_roles": ["admin"],
                "exact_match": False
            },
            "expected_permission": True
        },
        {
            "name": "检查经理权限(层级)",
            "data": {
                "required_roles": ["manager"],
                "exact_match": False
            },
            "expected_permission": True
        },
        {
            "name": "检查经理权限(精确)",
            "data": {
                "required_roles": ["manager"],
                "exact_match": True
            },
            "expected_permission": False
        },
        {
            "name": "检查多角色权限",
            "data": {
                "required_roles": ["admin", "manager"],
                "exact_match": False
            },
            "expected_permission": True
        }
    ]
    
    for test_case in test_cases:
        try:
            headers = {"Authorization": f"Bearer {admin_token}"}
            response = requests.post(
                f"{BASE_URL}/rbac/check-permission",
                headers=headers,
                json=test_case["data"],
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                has_permission = data.get("data", {}).get("has_permission", False)
                success = has_permission == test_case["expected_permission"]
                details = f"权限结果: {has_permission} | 期望: {test_case['expected_permission']}"
            else:
                success = False
                details = f"状态码: {response.status_code}"
            
            print_test_result(test_case["name"], success, details)
            
        except Exception as e:
            print_test_result(test_case["name"], False, f"错误: {str(e)}")

def test_user_permissions_endpoint():
    """测试用户权限信息端点"""
    print_section("用户权限信息测试")
    
    tokens = {
        "admin": create_access_token("admin"),
        "manager": create_access_token("manager"),
        "specialist": create_access_token("specialist")
    }
    
    for role, token in tokens.items():
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(
                f"{BASE_URL}/rbac/me/permissions",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                permissions_data = data.get("data", {})
                role_level = permissions_data.get("role_level", 0)
                permissions = permissions_data.get("permissions", [])
                
                success = True
                details = f"角色等级: {role_level} | 权限数量: {len(permissions)}"
            else:
                success = False
                details = f"状态码: {response.status_code}"
            
            print_test_result(f"{role.upper()}用户权限查询", success, details)
            
        except Exception as e:
            print_test_result(f"{role.upper()}用户权限查询", False, f"错误: {str(e)}")

def test_system_info_endpoint():
    """测试系统信息端点"""
    print_section("系统信息端点测试")
    
    admin_token = create_access_token("admin")
    manager_token = create_access_token("manager")
    
    test_cases = [
        {
            "name": "管理员访问系统信息",
            "token": admin_token,
            "should_pass": True
        },
        {
            "name": "经理访问系统信息",
            "token": manager_token,
            "should_pass": False
        }
    ]
    
    for test_case in test_cases:
        try:
            headers = {"Authorization": f"Bearer {test_case['token']}"}
            response = requests.get(
                f"{BASE_URL}/rbac/system-info",
                headers=headers,
                timeout=10
            )
            
            if test_case["should_pass"]:
                success = response.status_code == 200
                if success:
                    data = response.json()
                    system_data = data.get("data", {})
                    details = f"RBAC版本: {system_data.get('rbac_version', 'N/A')}"
                else:
                    details = f"状态码: {response.status_code}"
            else:
                success = response.status_code == 403
                details = f"状态码: {response.status_code} (期望403)"
            
            print_test_result(test_case["name"], success, details)
            
        except Exception as e:
            print_test_result(test_case["name"], False, f"错误: {str(e)}")

def main():
    """主函数"""
    print(f"RBAC功能测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("注意: 此测试需要后端服务运行在 http://localhost:8000")
    print("注意: 此测试使用模拟令牌，实际API调用可能因为用户不存在而失败")
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/rbac/system-info", timeout=5)
        service_running = True
    except:
        service_running = False
    
    if not service_running:
        print("\n❌ 后端服务未运行，请先启动服务:")
        print("   cd backend && python main.py")
        return
    
    print("\n✅ 后端服务正在运行")
    
    # 运行测试
    test_rbac_endpoints()
    test_permission_check_endpoint()
    test_user_permissions_endpoint()
    test_system_info_endpoint()
    
    print_section("测试完成")
    print("如果测试失败，请检查:")
    print("1. 后端服务是否正常运行")
    print("2. 数据库中是否有对应的测试用户")
    print("3. JWT密钥配置是否正确")

if __name__ == "__main__":
    main() 
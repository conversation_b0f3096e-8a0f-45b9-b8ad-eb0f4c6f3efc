name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: "3.10"
  NODE_VERSION: "18"

jobs:
  # 代码质量检查
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt
    
    - name: Run Black (代码格式化检查)
      run: |
        cd backend
        black --check .
    
    - name: Run isort (导入排序检查)
      run: |
        cd backend
        isort --check-only .
    
    - name: Run flake8 (代码风格检查)
      run: |
        cd backend
        flake8 .
    
    - name: Run mypy (类型检查)
      run: |
        cd backend
        mypy .

  # 后端测试
  test-backend:
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_intellibid
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt
    
    - name: Run tests with coverage
      env:
        DATABASE_URL: postgresql://test:test@localhost:5432/test_intellibid
      run: |
        cd backend
        pytest --cov=app --cov-report=xml --cov-report=html
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        fail_ci_if_error: true

  # Docker构建测试
  build:
    runs-on: ubuntu-latest
    needs: [lint, test-backend]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        target: production
        push: false
        tags: intellibid:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 部署到开发环境
  deploy-dev:
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to Development
      run: |
        echo "🚀 部署到开发环境"
        # 这里添加具体的部署脚本
        # 例如: kubectl apply -f k8s/dev/
    
    - name: Health Check
      run: |
        echo "🔍 执行健康检查"
        # 添加健康检查脚本

  # 部署到生产环境
  deploy-prod:
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to Production
      run: |
        echo "🚀 部署到生产环境"
        # 这里添加具体的生产部署脚本
    
    - name: Health Check
      run: |
        echo "🔍 执行生产环境健康检查"
        # 添加生产环境健康检查脚本 
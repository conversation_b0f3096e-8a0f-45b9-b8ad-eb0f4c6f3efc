---
type: "always_apply"
---
# 任务跟踪与状态更新规则

## 概述
此规则用于跟踪开发任务的进展状态，并在更新时需要用户确认。所有任务状态更新都会记录在 `任务与更新说明.md` 文件中。

## 任务状态定义
基于 [开发任务清单.md](docs/开发任务清单.md) 中的任务，定义以下状态：

### 状态类型
- **待开始 (TODO)**: 任务已定义但尚未开始
- **进行中 (IN_PROGRESS)**: 任务正在开发中
- **已完成 (COMPLETED)**: 任务已完成并通过验证
- **已暂停 (PAUSED)**: 任务暂时停止，等待依赖或资源
- **需修改 (REVISION)**: 任务需要根据反馈进行修改

### 优先级
- **P0 (Critical)**: 关键路径任务
- **P1 (High)**: MVP核心功能
- **P2 (Medium)**: 重要功能
- **P3 (Low)**: 优化项

## 任务更新流程

### 1. 状态更新确认
在更新任务状态之前，必须：
1. 向用户展示当前任务状态
2. 说明要进行的状态变更
3. 等待用户明确确认
4. 获得确认后才能更新状态

### 2. 更新记录格式
每次状态更新都需要记录：
- 任务ID和名称
- 状态变更（从 → 到）
- **更新时间**: 必须使用真实的系统当前时间
- 完成内容描述
- 本次迭代新增的具体内容
- 遇到的问题和解决方案

### 3. 文件更新
所有更新记录保存在根目录的 `任务与更新说明.md` 文件中。

## 更新模板

### 任务完成记录模板
```markdown
## 任务更新记录

### 📋 任务信息
- **任务ID**: CORE-101
- **任务名称**: 初始化FastAPI项目，配置Docker环境和CI/CD基础流水线
- **状态变更**: 进行中 → 已完成
- **更新时间**: 2025-01-14T15:49:09+08:00
- **负责人**: [开发者姓名]

### ✅ 完成内容
- [x] 创建FastAPI项目结构
- [x] 配置Docker环境
- [x] 设置CI/CD基础流水线
- [x] 添加基础依赖管理

### 🆕 本次迭代新增内容
- 添加了自动化测试流水线
- 集成了代码质量检查工具
- 配置了开发环境的热重载功能
- 建立了统一的错误处理机制

### 🔧 技术实现细节
- 使用FastAPI 0.104.1版本
- Docker多阶段构建优化镜像大小
- GitHub Actions配置自动化部署
- 添加了健康检查端点

### ⚠️ 遇到的问题
- Docker构建时间较长，通过多阶段构建和缓存优化解决
- CI/CD权限配置问题，通过环境变量管理解决

### 📝 备注
- 项目基础架构已就绪，可以开始后续功能开发
- 建议下一步开始用户认证模块开发
```

## 使用指南

### 开发者使用
1. 开始任务时
</augment_code_snippet>
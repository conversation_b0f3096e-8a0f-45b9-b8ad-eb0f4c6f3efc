#!/usr/bin/env python3
"""
创建测试用户脚本
用于生成系统测试所需的用户账号
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.database import engine
from app.models.user import User
from app.core.security import get_password_hash


def create_test_users():
    """创建测试用户"""
    print("正在创建测试用户...")
    
    # 测试用户数据
    test_users = [
        {
            "username": "admin",
            "password": "admin123",
            "role": "admin",
            "description": "系统管理员"
        },
        {
            "username": "manager",
            "password": "manager123", 
            "role": "manager",
            "description": "项目经理"
        },
        {
            "username": "specialist",
            "password": "spec123",
            "role": "specialist", 
            "description": "专员"
        },
        {
            "username": "testuser",
            "password": "test123",
            "role": "specialist",
            "description": "测试用户"
        }
    ]
    
    try:
        with Session(engine) as session:
            for user_data in test_users:
                # 检查用户是否已存在
                existing_user = session.query(User).filter(
                    User.username == user_data["username"]
                ).first()
                
                if existing_user:
                    print(f"用户 {user_data['username']} 已存在，跳过创建")
                    continue
                
                # 创建新用户
                hashed_password = get_password_hash(user_data["password"])
                new_user = User(
                    username=user_data["username"],
                    hashed_password=hashed_password,
                    role=user_data["role"]
                )
                
                session.add(new_user)
                session.commit()
                session.refresh(new_user)
                
                print(f"✅ 创建用户: {user_data['username']} ({user_data['description']}) - 密码: {user_data['password']}")
        
        print("\n🎉 测试用户创建完成！")
        print("\n📋 测试账号列表:")
        print("=" * 60)
        print("| 用户名      | 密码        | 角色        | 描述         |")
        print("=" * 60)
        for user_data in test_users:
            print(f"| {user_data['username']:<10} | {user_data['password']:<10} | {user_data['role']:<10} | {user_data['description']:<10} |")
        print("=" * 60)
        print("\n🌐 访问地址:")
        print("- 前端: http://localhost:3001")
        print("- 后端API: http://localhost:8000")
        print("- API文档: http://localhost:8000/docs")
        
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    create_test_users() 
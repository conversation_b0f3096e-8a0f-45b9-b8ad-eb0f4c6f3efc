import asyncio
import asyncpg
import sys

# 数据库配置
DATABASE_URL = "*******************************************************************/postgres"

async def test_connection():
    """测试数据库连接"""
    try:
        print("正在测试数据库连接...")
        print(f"连接URL: {DATABASE_URL}")
        
        # 尝试连接数据库
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ 数据库连接成功!")
        
        # 测试基本查询
        version = await conn.fetchval("SELECT version()")
        print(f"PostgreSQL版本: {version}")
        
        # 检查是否有pgvector扩展
        try:
            extensions = await conn.fetch("SELECT extname FROM pg_extension WHERE extname = 'vector'")
            if extensions:
                print("✅ pgvector扩展已安装")
            else:
                print("⚠️  pgvector扩展未安装")
        except Exception as e:
            print(f"⚠️  检查pgvector扩展时出错: {e}")
        
        # 检查数据库权限
        try:
            # 尝试创建一个测试表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS test_connection (
                    id SERIAL PRIMARY KEY,
                    test_data TEXT
                )
            """)
            print("✅ 具有创建表的权限")
            
            # 清理测试表
            await conn.execute("DROP TABLE IF EXISTS test_connection")
            print("✅ 具有删除表的权限")
            
        except Exception as e:
            print(f"❌ 权限检查失败: {e}")
        
        await conn.close()
        print("✅ 连接测试完成")
        return True
        
    except asyncpg.InvalidAuthorizationSpecificationError:
        print("❌ 认证失败: 用户名或密码错误")
        return False
    except asyncpg.InvalidCatalogNameError:
        print("❌ 数据库不存在")
        return False
    except asyncpg.CannotConnectNowError:
        print("❌ 无法连接到数据库服务器")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(test_connection())
        if result:
            print("\n🎉 数据库连接验证成功，可以继续创建测试用户")
            sys.exit(0)
        else:
            print("\n💥 数据库连接验证失败，请检查配置")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  测试被中断")
        sys.exit(1) 
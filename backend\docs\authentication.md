# 认证API使用指南

## 概述

IntelliBid系统提供完整的JWT认证机制，支持用户注册、登录、令牌刷新等功能。

## API端点

### 认证相关

| 端点 | 方法 | 描述 | 权限要求 |
|------|------|------|----------|
| `/api/v1/auth/register` | POST | 用户注册 | 无 |
| `/api/v1/auth/login` | POST | OAuth2密码流登录 | 无 |
| `/api/v1/auth/login/json` | POST | JSON格式登录 | 无 |
| `/api/v1/auth/refresh` | POST | 刷新访问令牌 | 无 |
| `/api/v1/auth/me` | GET | 获取当前用户信息 | 需要认证 |
| `/api/v1/auth/logout` | POST | 用户登出 | 需要认证 |
| `/api/v1/auth/change-password` | POST | 修改密码 | 需要认证 |

### 用户管理

| 端点 | 方法 | 描述 | 权限要求 |
|------|------|------|----------|
| `/api/v1/users/` | GET | 获取用户列表 | 经理或管理员 |
| `/api/v1/users/{user_id}` | GET | 获取用户详情 | 经理或管理员 |
| `/api/v1/users/` | POST | 创建用户 | 管理员 |
| `/api/v1/users/{user_id}` | PUT | 更新用户 | 本人或管理员 |
| `/api/v1/users/{user_id}` | DELETE | 停用用户 | 管理员 |
| `/api/v1/users/profile/me` | GET | 获取个人资料 | 需要认证 |
| `/api/v1/users/profile/me` | PUT | 更新个人资料 | 需要认证 |

## 使用示例

### 1. 用户注册

```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "specialist",
    "full_name": "Test User"
  }'
```

响应：
```json
{
  "id": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "role": "specialist",
  "full_name": "Test User",
  "is_active": true,
  "created_at": "2025-07-14T16:37:44+08:00",
  "updated_at": "2025-07-14T16:37:44+08:00"
}
```

### 2. 用户登录

```bash
curl -X POST "http://localhost:8000/api/v1/auth/login/json" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

响应：
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 3. 使用访问令牌

```bash
curl -X GET "http://localhost:8000/api/v1/auth/me" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 4. 刷新令牌

```bash
curl -X POST "http://localhost:8000/api/v1/auth/refresh" \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

## 用户角色

系统支持三种用户角色：

### 1. specialist (专员)
- 可以查看分配给自己的任务
- 可以编辑投标书内容
- 受限的项目访问权限

### 2. manager (项目经理)
- 拥有专员的所有权限
- 可以创建和管理项目
- 可以分配任务给专员
- 可以查看所有项目数据
- 可以查看用户列表

### 3. admin (系统管理员)
- 拥有经理的所有权限
- 可以管理LLM配置
- 可以管理用户账户
- 可以创建/更新/停用用户

## 错误处理

### 常见错误码

- `400 Bad Request` - 请求参数错误
- `401 Unauthorized` - 未认证或令牌无效
- `403 Forbidden` - 权限不足
- `404 Not Found` - 资源不存在
- `422 Unprocessable Entity` - 数据验证失败

### 错误响应格式

```json
{
  "detail": "错误描述信息"
}
```

## 安全注意事项

1. **密码要求**: 最少6个字符
2. **令牌过期**: 访问令牌默认30分钟过期，刷新令牌7天过期
3. **HTTPS**: 生产环境必须使用HTTPS
4. **密钥管理**: 修改默认的SECRET_KEY
5. **默认账户**: 生产环境部署后立即修改默认管理员密码

## 测试

运行API测试脚本：

```bash
# 确保服务器正在运行
python backend/scripts/test_api.py

# 运行单元测试
pytest backend/tests/test_auth_api.py -v
``` 
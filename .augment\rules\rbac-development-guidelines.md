---
type: "always_apply"
---
# RBAC开发指导原则

## 强制性安全要求
**重要**: 所有新的API端点都必须实现适当的权限控制，这是强制性的安全要求。

## 权限等级定义

### 用户角色
- **Admin (管理员)**: 系统管理员，拥有所有权限
- **Manager (经理)**: 项目经理，可管理项目和分配任务  
- **Specialist (专员)**: 技术专员，只能访问分配的任务

### 权限层级
```
Admin (level 3) > Manager (level 2) > Specialist (level 1)
```

## 测试用户账号
- **admin/admin123** - 管理员测试
- **manager/manager123** - 经理测试  
- **specialist/specialist123** - 专员测试

## RBAC实现模式

### 1. 导入必要模块
```python
from app.core.rbac import (
    require_admin,
    require_manager, 
    require_specialist,
    require_role,
    check_project_access
)
from app.models.user import User
from fastapi import Depends
```

### 2. 路由级权限控制
```python
@router.post("/admin-only")
async def admin_endpoint(current_user: User = Depends(require_admin())):
    # 仅管理员可访问
    pass

@router.get("/manager-and-above") 
async def manager_endpoint(current_user: User = Depends(require_manager())):
    # 经理及以上权限可访问
    pass

@router.get("/all-authenticated")
async def specialist_endpoint(current_user: User = Depends(require_specialist())):
    # 所有认证用户可访问
    pass
```

### 3. 多角色权限控制
```python
@router.put("/projects/{project_id}")
async def update_project(
    project_id: int,
    current_user: User = Depends(require_role(["admin", "manager"]))
):
    # 管理员或经理可访问
    pass
```

### 4. 资源级权限控制
```python
@router.get("/projects/{project_id}")
async def get_project(
    project_id: int,
    current_user: User = Depends(require_specialist())
):
    # 检查用户是否有访问该项目的权限
    if not check_project_access(current_user, project_id):
        raise HTTPException(status_code=403, detail="无权访问该项目")
    
    # 业务逻辑
    pass
```

## 权限检查函数

### 基础权限检查
```python
def require_admin():
    """要求管理员权限"""
    def check_admin(current_user: User = Depends(get_current_user)):
        if current_user.role != "admin":
            raise HTTPException(status_code=403, detail="需要管理员权限")
        return current_user
    return check_admin

def require_manager():
    """要求经理及以上权限"""
    def check_manager(current_user: User = Depends(get_current_user)):
        if current_user.role not in ["admin", "manager"]:
            raise HTTPException(status_code=403, detail="需要经理或管理员权限")
        return current_user
    return check_manager
```

### 资源访问控制
```python
def check_project_access(user: User, project_id: int) -> bool:
    """检查用户是否有访问指定项目的权限"""
    if user.role == "admin":
        return True
    elif user.role == "manager":
        # 经理可以访问自己创建的项目
        return user.id == get_project_creator(project_id)
    elif user.role == "specialist":
        # 专员只能访问分配给自己的项目
        return user.id in get_project_assignees(project_id)
    return False
```

## 开发检查清单

在开发任何新的API端点时，必须检查：

- [ ] **导入RBAC模块**: 是否导入了必要的权限检查函数？
- [ ] **路由权限**: 是否在路由函数中添加了权限检查依赖？
- [ ] **权限级别**: 是否选择了正确的权限级别？
- [ ] **资源权限**: 是否在业务逻辑中进行了细粒度权限检查？
- [ ] **错误处理**: 是否提供了清晰的权限错误信息？
- [ ] **测试用例**: 是否编写了权限测试用例？
- [ ] **API文档**: 是否在API文档中标注了权限要求？

## 错误处理标准

### 权限错误响应
```python
# 401 - 未认证
{
    "code": 401,
    "message": "未认证，请先登录",
    "details": {}
}

# 403 - 权限不足
{
    "code": 403, 
    "message": "权限不足，需要管理员权限",
    "details": {
        "required_role": "admin",
        "current_role": "specialist"
    }
}
```

## 测试指南

### 权限测试用例
```python
def test_admin_only_endpoint():
    # 测试管理员访问
    response = client.post("/admin-only", headers=admin_headers)
    assert response.status_code == 200
    
    # 测试非管理员访问
    response = client.post("/admin-only", headers=specialist_headers)
    assert response.status_code == 403

def test_project_access_control():
    # 测试项目创建者访问
    response = client.get(f"/projects/{project_id}", headers=manager_headers)
    assert response.status_code == 200
    
    # 测试非项目成员访问
    response = client.get(f"/projects/{project_id}", headers=other_user_headers)
    assert response.status_code == 403
```

## 最佳实践

1. **最小权限原则**: 给用户分配完成任务所需的最小权限
2. **权限分离**: 管理权限和业务权限分离
3. **审计日志**: 记录所有权限相关的操作
4. **定期审查**: 定期审查用户权限和角色分配
5. **安全测试**: 每个端点都要进行权限测试
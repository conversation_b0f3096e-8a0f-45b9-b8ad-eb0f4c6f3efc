---
description: 
globs: 
alwaysApply: true
---
# 前端编辑器开发指南

## ProseMirror编辑器架构
基于 [系统需求规则说明书.md](mdc:docs/系统需求规则说明书.md) 中的F-3.5需求，系统采用ProseMirror构建智能编辑器。

## 自定义Schema设计

### 投标书文档结构
```javascript
const bidDocumentSchema = new Schema({
  nodes: {
    doc: {
      content: "chapter+"
    },
    chapter: {
      content: "chapter_title section*",
      attrs: {
        id: { default: null },
        level: { default: 1 }
      }
    },
    chapter_title: {
      content: "text*",
      attrs: {
        level: { default: 1 }
      }
    },
    section: {
      content: "paragraph+ | compliance_block | scoring_point_block"
    },
    compliance_block: {
      content: "paragraph+",
      attrs: {
        requirement: { default: "" },
        status: { default: "pending" } // pending, compliant, non-compliant
      }
    },
    scoring_point_block: {
      content: "paragraph+",
      attrs: {
        points: { default: 0 },
        category: { default: "" }
      }
    },
    paragraph: {
      content: "text*",
      marks: "strong em underline"
    },
    text: {
      group: "inline"
    }
  },
  marks: {
    strong: {},
    em: {},
    underline: {}
  }
})
```

## AI集成功能

### 编辑器内AI操作
基于光标位置和选中内容，动态显示AI操作选项：

#### 内容生成
- **触发条件**: 光标在空的章节下
- **操作**: 显示"✨ 生成本章内容"按钮
- **实现**: 调用`/api/v1/projects/{projectId}/generate-chapter`

#### 内容优化
- **触发条件**: 用户选中文字
- **操作选项**:
  - "✍️ 优化润色" - 改善文字表达
  - "↔️ 扩写内容" - 增加详细说明
  - "➡️ 缩写摘要" - 压缩为要点
  - "👔 切换风格" - 调整正式程度

### AI操作实现
```javascript
// AI优化操作
async function optimizeText(text, action, llmModelId) {
  const response = await fetch('/api/v1/ai/optimize-text', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      text,
      action, // 'polish', 'expand', 'summarize', 'style'
      llmModelId
    })
  });
  
  return await response.json();
}

// 流式内容生成
async function generateChapterContent(chapterId, llmModelId) {
  const response = await fetch(`/api/v1/projects/${projectId}/generate-chapter`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      chapterId,
      llmModelId
    })
  });
  
  // 处理流式响应
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    const chunk = decoder.decode(value);
    // 实时更新编辑器内容
    updateEditorContent(chunk);
  }
}
```

## 编辑器插件系统

### 核心插件

#### AI助手插件
```javascript
import { Plugin } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';

const aiAssistantPlugin = new Plugin({
  key: new PluginKey('ai-assistant'),
  
  state: {
    init() {
      return {
        showAIMenu: false,
        selectedText: null,
        availableModels: []
      };
    },
    
    apply(tr, value, oldState, newState) {
      // 处理选择变化，显示/隐藏AI菜单
      const selection = newState.selection;
      if (!selection.empty) {
        return {
          ...value,
          showAIMenu: true,
          selectedText: newState.doc.textBetween(selection.from, selection.to)
        };
      }
      return { ...value, showAIMenu: false };
    }
  },
  
  props: {
    decorations(state) {
      // 添加AI操作装饰
      const { showAIMenu } = this.getState(state);
      if (showAIMenu) {
        return DecorationSet.create(state.doc, [
          Decoration.widget(state.selection.to, createAIMenu)
        ]);
      }
      return DecorationSet.empty;
    }
  }
});
```

#### 结构化编辑插件
```javascript
const structuredEditPlugin = new Plugin({
  key: new PluginKey('structured-edit'),
  
  props: {
    handleKeyDown(view, event) {
      // 处理结构化编辑快捷键
      if (event.key === 'Enter' && event.ctrlKey) {
        // 创建新章节
        insertChapter(view);
        return true;
      }
      return false;
    }
  }
});
```

## 文档导出功能

### 导出流程
1. 获取ProseMirror文档JSON
2. 调用后端导出服务
3. 下载生成的文件

```javascript
async function exportDocument(format) {
  const docJson = view.state.doc.toJSON();
  
  const response = await fetch('/internal/v1/export', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      proseMirrorJson: docJson,
      format // 'docx' or 'pdf'
    })
  });
  
  const result = await response.json();
  
  // 下载文件
  window.open(result.downloadUrl, '_blank');
}
```

### 导出格式支持
- **PDF**: 使用无头浏览器 + 打印CSS
- **DOCX**: 使用python-docx程序化构建
- **HTML**: 直接从ProseMirror输出

## 用户体验优化

### 自动保存
```javascript
const autoSavePlugin = new Plugin({
  key: new PluginKey('auto-save'),
  
  state: {
    init() {
      return { lastSaved: Date.now() };
    },
    
    apply(tr, value) {
      if (tr.docChanged) {
        // 文档发生变化，标记需要保存
        return { ...value, needsSave: true };
      }
      return value;
    }
  },
  
  view(editorView) {
    const saveInterval = setInterval(() => {
      const state = this.getState(editorView.state);
      if (state.needsSave) {
        saveBidDocument(editorView.state.doc.toJSON());
      }
    }, 30000); // 每30秒自动保存
    
    return {
      destroy() {
        clearInterval(saveInterval);
      }
    };
  }
});
```

### 协作编辑(未来功能)
```javascript
// 基于Y.js的协作编辑
import * as Y from 'yjs';
import { ySyncPlugin, yCursorPlugin } from 'y-prosemirror';

const ydoc = new Y.Doc();
const yXmlFragment = ydoc.getXmlFragment('prosemirror');

const collaborationPlugins = [
  ySyncPlugin(yXmlFragment),
  yCursorPlugin(provider.awareness)
];
```

## 性能优化

### 虚拟滚动
对于大型文档，实现虚拟滚动以提高性能：

```javascript
const virtualScrollPlugin = new Plugin({
  key: new PluginKey('virtual-scroll'),
  
  view(editorView) {
    // 实现虚拟滚动逻辑
    return new VirtualScrollView(editorView);
  }
});
```

### 懒加载
章节内容按需加载，减少初始加载时间：

```javascript
async function loadChapterContent(chapterId) {
  const response = await fetch(`/api/v1/chapters/${chapterId}/content`);
  const content = await response.json();
  
  // 动态插入到编辑器
  insertChapterContent(content);
}
```

## 测试策略

### 单元测试
- Schema验证测试
- 插件功能测试
- AI集成测试

### 集成测试
- 编辑器与后端API集成
- 文档导出流程测试
- 用户交互测试

### 性能测试
- 大文档加载性能
- AI操作响应时间
- 内存使用优化



### **智能标书生成系统：开发任务清单 (Development Task List)**

**项目名称**: 赢标智绘引擎 / Project IntelliBid Orchestrator **版本**: 1.0

#### **优先级定义**:

- **P0 (Critical)**: 关键路径任务，无此功能则核心流程不通。
- **P1 (High)**: 核心功能，构成MVP（最小可行产品）的必要部分。
- **P2 (Medium)**: 重要功能，提升用户体验和系统完整性。
- **P3 (Low)**: 优化项或高级功能，可在后续迭代中实现。

**复杂度定义 (T-Shirt Sizing)**:

- **S (Small)**: 1-2天
- **M (Medium)**: 3-5天
- **L (Large)**: 5-10天
- **XL (Extra Large)**: >10天 (通常需要进一步拆分)

------

### **Epic 1: 核心框架与用户认证 (Core Framework & User Authentication)**

**目标**: 搭建项目的基础框架，实现用户登录、注册和权限管理，为后续所有功能提供基础。

| 任务ID       | 任务描述                                                     | 优先级 | 技术栈          | 复杂度 | 依赖     |
| ------------ | ------------------------------------------------------------ | ------ | --------------- | ------ | -------- |
| **CORE-101** | **【后端】** 初始化FastAPI项目，配置Docker环境和CI/CD基础流水线 | **P0** | Backend, DevOps | M      | -        |
| **CORE-102** | **【前端】** 初始化React (Vite)项目，配置基本路由和布局      | **P0** | Frontend        | S      | -        |
| **CORE-103** | **【数据库】** 设计并创建`users`表，包含角色字段             | **P0** | Database        | S      | -        |
| **CORE-104** | **【后端】** 开发用户注册、登录API，集成JWT认证              | **P1** | Backend         | M      | CORE-103 |
| **CORE-105** | **【前端】** 开发登录、注册页面，并实现API对接与Token存储    | **P1** | Frontend        | M      | CORE-104 |
| **CORE-106** | **【后端】** 实现基于角色的访问控制（RBAC）中间件            | **P1** | Backend         | M      | CORE-104 |

### **Epic 2: 文档解析与AI分析流程 (Document Parsing & AI Analysis)**

**目标**: 实现从上传招标文件到生成结构化分析报告的完整MVP流程。

| 任务ID      | 任务描述                                                     | 优先级 | 技术栈          | 复杂度 | 依赖             |
| ----------- | ------------------------------------------------------------ | ------ | --------------- | ------ | ---------------- |
| **PAR-201** | **【后端】** 开发项目管理API (`projects`表的CRUD)            | **P1** | Backend         | M      | CORE-106         |
| **PAR-202** | **【数据库】** 设计并创建`projects`, `tender_documents`等核心业务表 | **P1** | Database        | S      | -                |
| **PAR-203** | **【前端】** 开发项目列表和创建项目页面                      | **P1** | Frontend        | M      | PAR-201          |
| **PAR-204** | **【后端】** 集成对象存储(MinIO/S3)，开发文件上传接口        | **P1** | Backend         | M      | -                |
| **PAR-205** | **【引擎】** 实现**文档解析引擎**，支持从.docx/.pdf提取文本  | **P1** | Backend, Engine | L      | PAR-204          |
| **PAR-206** | **【后端】** 开发工作流，编排“上传->保存->解析->存文本”的流程 | **P1** | Backend         | L      | PAR-205          |
| **PAR-207** | **【后端】** 实现**LLM服务层**的基础调用逻辑 (默认连接DeepSeek) | **P1** | Backend, LLM    | M      | -                |
| **PAR-208** | **【后端】** 实现`Prompt_Step1_Analysis`的调用链，生成分析报告并存库 | **P1** | Backend         | M      | PAR-206, PAR-207 |
| **PAR-209** | **【前端】** 开发分析报告的可视化展示页面                    | **P2** | Frontend        | M      | PAR-208          |

### **Epic 3: 多LLM引擎支持 (Multi-LLM Engine Support)**

**目标**: 实现LLM的可插拔和动态切换，满足成本与安全需求。

| 任务ID      | 任务描述                                                     | 优先级 | 技术栈          | 复杂度 | 依赖             |
| ----------- | ------------------------------------------------------------ | ------ | --------------- | ------ | ---------------- |
| **LLM-301** | **【数据库】** 设计并创建`llm_configs`表                     | **P1** | Database        | S      | -                |
| **LLM-302** | **【后端】** 重构LLM服务层，实现**适配器模式**，分离网关与具体实现 | **P1** | Backend, LLM    | L      | PAR-207          |
| **LLM-303** | **【后端】** 开发`DeepSeekAdapter`和`OpenAIAdapter`          | **P1** | Backend, LLM    | M      | LLM-302          |
| **LLM-304** | **【前端】** 开发**管理后台**基础，实现LLM引擎配置中心(CRUD) | **P2** | Frontend        | L      | LLM-301, LLM-302 |
| **LLM-305** | **【后端】** 在相关API中增加`llmModelId`参数，实现动态路由   | **P2** | Backend         | M      | LLM-302          |
| **LLM-306** | **【前端】** 在操作界面（如生成章节）增加LLM选择下拉菜单     | **P2** | Frontend        | S      | LLM-305          |
| **LLM-307** | **【后端】** 实现私有化部署模型的适配器及网络安全策略        | **P3** | Backend, DevOps | XL     | LLM-302          |
| **LLM-308** | **【后端】** 实现对在线API的Token用量计量与日志记录          | **P2** | Backend         | M      | LLM-302          |
| **LLM-309** | **【前端】** 开发成本监控仪表盘                              | **P3** | Frontend        | M      | LLM-308          |

### **Epic 4: 知识库(RAG)与内容生成 (Knowledge Base & Content Generation)**

**目标**: 建立企业知识库，并打通基于RAG的内容生成流程。

| 任务ID      | 任务描述                                                     | 优先级 | 技术栈             | 复杂度 | 依赖             |
| ----------- | ------------------------------------------------------------ | ------ | ------------------ | ------ | ---------------- |
| **RAG-401** | **【数据库】** 设计并创建`knowledge_items`表(含`vector`列)及HNSW索引 | **P1** | Database, pgvector | M      | -                |
| **RAG-402** | **【引擎】** 开发**知识库管理服务**，实现文档入库(分块、向量化) | **P2** | Backend, Engine    | L      | RAG-401          |
| **RAG-403** | **【引擎】** 开发知识库检索API，支持向量搜索和元数据过滤     | **P2** | Backend, Engine    | M      | RAG-402          |
| **RAG-404** | **【后端】** 开发`generate-outline` API，实现大纲生成        | **P2** | Backend, LLM       | M      | PAR-208          |
| **RAG-405** | **【后端】** 开发`generate-chapter` API，集成RAG流程(检索+LLM) | **P2** | Backend            | L      | RAG-403, LLM-305 |
| **RAG-406** | **【前端】** 开发知识库管理界面（上传源文档）                | **P3** | Frontend           | M      | RAG-402          |

### **Epic 5: 智能编辑器与文档导出 (Intelligent Editor & Export)**

**目标**: 提供所见即所得的、具备AI能力的编辑体验，并能高保真导出成果。

| 任务ID       | 任务描述                                                     | 优先级 | 技术栈          | 复杂度 | 依赖              |
| ------------ | ------------------------------------------------------------ | ------ | --------------- | ------ | ----------------- |
| **EDIT-501** | **【前端】** 集成ProseMirror，并设计实现投标书的自定义Schema | **P2** | Frontend        | L      | -                 |
| **EDIT-502** | **【前端】** 开发投标书编辑界面，实现内容的加载与保存(对接`bid_documents` API) | **P2** | Frontend        | L      | EDIT-501, RAG-405 |
| **EDIT-503** | **【前端】** 实现“生成本章内容”按钮，并流式更新编辑器内容    | **P2** | Frontend        | M      | RAG-405, EDIT-501 |
| **EDIT-504** | **【前端】** 开发上下文菜单，并对接“优化润色”、“扩写”等AI优化API | **P2** | Frontend        | L      | EDIT-501          |
| **EDIT-505** | **【引擎】** 实现**文档导出服务**，支持ProseMirror JSON到PDF | **P2** | Backend, Engine | L      | -                 |
| **EDIT-506** | **【引擎】** 增强文档导出服务，支持ProseMirror JSON到DOCX    | **P2** | Backend, Engine | XL     | EDIT-505          |
| **EDIT-507** | **【后端】** 开发触发文档导出的API                           | **P2** | Backend         | S      | EDIT-505          |
| **EDIT-508** | **【前端】** 在编辑界面添加入“导出PDF/Word”按钮              | **P2** | Frontend        | S      | EDIT-507          |

### **Epic 6: 部署、监控与运维 (Deployment, Monitoring & DevOps)**

**目标**: 确保系统稳定、安全、可观测地运行在生产环境。

| 任务ID      | 任务描述                                                     | 优先级 | 技术栈           | 复杂度 | 依赖    |
| ----------- | ------------------------------------------------------------ | ------ | ---------------- | ------ | ------- |
| **OPS-601** | **【DevOps】** 编写所有服务的Dockerfile和docker-compose文件  | **P1** | DevOps           | M      | -       |
| **OPS-602** | **【DevOps】** 编写Kubernetes部署所需的YAML文件(Deployment, Service, Ingress) | **P2** | DevOps           | L      | OPS-601 |
| **OPS-603** | **【DevOps】** 配置生产环境的PostgreSQL、Redis、MinIO        | **P2** | DevOps, Database | L      | -       |
| **OPS-604** | **【DevOps】** 建立生产环境的CI/CD流水线，实现自动化部署     | **P2** | DevOps           | L      | OPS-602 |
| **OPS-605** | **【DevOps】** 部署Prometheus + Grafana监控系统，并为关键服务添加仪表盘 | **P2** | DevOps           | L      | OPS-602 |
| **OPS-606** | **【DevOps】** 配置Alertmanager，实现关键故障告警            | **P3** | DevOps           | M      | OPS-605 |

------

**建议的开发迭代计划 (Sprint Plan Suggestion):**

- **Sprint 1 (核心MVP)**: 聚焦于**Epic 1**的全部P0/P1任务，以及**Epic 2**的P1任务。目标是实现用户能登录，并能上传文档看到初步的AI分析结果。
- **Sprint 2 (RAG与生成)**: 聚焦于**Epic 4**的P1/P2任务和**Epic 5**的`EDIT-501, 502, 503`。目标是让用户能够在编辑器里生成内容。
- **Sprint 3 (多模型与AI优化)**: 聚焦于**Epic 3**的P1/P2任务和**Epic 5**的`EDIT-504`。目标是实现LLM切换和编辑器内的智能优化。
- **Sprint 4 (导出与完善)**: 聚焦于**Epic 5**的P2导出任务，并完善前端体验。
- **并行任务**: **Epic 6**的DevOps任务应与开发并行进行，尽早搭建好测试和预生产环境。
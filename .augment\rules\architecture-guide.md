---
type: "always_apply"
---
# 系统架构指导原则

## 架构愿景
采用**分层与模块化的服务架构**，由四大核心层次构成：前端应用层、应用服务层、核心引擎层、数据与存储层。

## 设计原则
- **分层与模块化**: 严格遵循关注点分离原则，将系统划分为清晰的层次和独立的模块
- **服务化**: 将核心的、资源密集型功能封装成独立的服务，通过定义良好的API进行交互
- **高可用与可扩展**: 支持水平扩展，确保在用户量和数据量增长时系统性能稳定
- **安全优先**: 将数据安全和访问控制贯穿于架构的每一层
- **可配置与可维护**: 关键业务逻辑必须外部化，允许非开发人员进行迭代优化

## 核心服务层次

### 前端应用层
- **用户工作台**: 面向投标团队的核心操作界面(SPA)
- **管理后台**: 面向系统管理员的独立SPA

### 应用服务层
- **API网关**: 统一的请求入口，负责路由、认证、限流
- **工作流编排服务**: 核心业务逻辑，实现LangChain业务流程
- **用户与权限管理服务**: 管理用户账户、角色和权限
- **项目与文档管理服务**: 项目、文档的CRUD操作及元数据管理

### 核心引擎层
- **文档解析引擎**: 将文档转换为纯文本或结构化文本
- **LLM服务层**: 实现适配器模式，统一LLM调用接口
- **文档导出服务**: 服务端高保真格式转换

### 数据与存储层
- **PostgreSQL**: 主数据库，存储用户、项目、文档等结构化数据
- **pgvector**: 向量数据库扩展，支持知识库相似度搜索
- **MinIO**: 对象存储，存储上传的文档和生成的文件
- **Redis**: 缓存层，提升系统响应性能

## 技术选型

### 后端技术栈
- **语言**: Python 3.10+
- **框架**: FastAPI (高性能异步框架)
- **AI框架**: LangChain (工作流编排)
- **数据库**: PostgreSQL + pgvector
- **缓存**: Redis
- **消息队列**: Celery + Redis

### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI库**: Ant Design Pro
- **状态管理**: Redux Toolkit
- **编辑器**: ProseMirror

### 部署与运维
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes (生产环境)
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **CI/CD**: GitHub Actions

## 数据流架构

### 核心业务流程
1. **文档上传** → **解析引擎** → **结构化存储**
2. **AI分析** → **LLM服务层** → **分析报告生成**
3. **大纲生成** → **知识库检索** → **内容生成**
4. **编辑器操作** → **实时保存** → **版本管理**
5. **导出请求** → **格式转换** → **文件下载**

### 服务间通信
- **同步调用**: HTTP/REST API
- **异步处理**: 消息队列
- **实时通信**: WebSocket (编辑器协作)

## 安全架构

### 认证授权
- **JWT Token**: 无状态认证
- **RBAC**: 基于角色的访问控制
- **API网关**: 统一的安全入口

### 数据安全
- **传输加密**: HTTPS/TLS
- **存储加密**: 敏感数据加密存储
- **访问控制**: 细粒度权限管理
- **审计日志**: 完整的操作记录

## 性能优化

### 缓存策略
- **应用缓存**: Redis缓存热点数据
- **CDN**: 静态资源分发
- **数据库优化**: 索引优化和查询优化

### 扩展性设计
- **水平扩展**: 无状态服务设计
- **负载均衡**: 多实例部署
- **数据分片**: 大数据量处理
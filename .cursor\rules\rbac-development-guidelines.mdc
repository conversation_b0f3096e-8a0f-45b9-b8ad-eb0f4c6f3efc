---
description: 
globs: 
alwaysApply: true
---
# RBAC开发指导原则

## 概述
在IntelliBid系统的所有后续开发中，必须严格遵循基于角色的访问控制(RBAC)原则。所有新的API端点都必须实现适当的权限控制。

## 强制性规则

### 1. API端点权限控制
**所有新的API端点都必须包含权限检查**，没有例外。

#### 权限等级定义
- **Admin (管理员)**: 系统管理员，拥有所有权限
- **Manager (经理)**: 项目经理，可管理项目和分配任务
- **Specialist (专员)**: 技术专员，只能访问分配的任务

#### 权限层级
```
Admin (level 3) > Manager (level 2) > Specialist (level 1)
```

### 2. 必须导入的RBAC模块
```python
from app.core.rbac import (
    require_admin,
    require_manager,
    require_specialist,
    require_roles,
    require_any_role,
    get_current_user_from_token,
    check_user_permission,
    get_user_permissions
)
from app.models.user import User, UserRole
```

### 3. API端点权限模板

#### 管理员专用端点
```python
@router.post("/admin-operation")
async def admin_operation(
    data: SomeSchema,
    current_user: User = Depends(require_admin())
):
    """仅管理员可执行的操作"""
    # 实现逻辑
    pass
```

#### 经理及以上权限端点
```python
@router.post("/projects")
async def create_project(
    project_data: ProjectCreate,
    current_user: User = Depends(require_manager())
):
    """创建项目 - 需要经理权限"""
    # 经理和管理员可以创建项目
    pass
```

#### 专员及以上权限端点
```python
@router.get("/my-tasks")
async def get_my_tasks(
    current_user: User = Depends(require_specialist())
):
    """获取我的任务 - 任何角色都可以"""
    # 所有角色都可以查看任务
    pass
```

#### 多角色权限端点
```python
@router.put("/projects/{project_id}")
async def update_project(
    project_id: int,
    project_data: ProjectUpdate,
    current_user: User = Depends(require_roles(UserRole.ADMIN, UserRole.MANAGER))
):
    """更新项目 - 管理员或经理可以"""
    # 管理员或经理可以更新项目
    pass
```

#### 精确角色匹配端点
```python
@router.get("/manager-only-reports")
async def manager_reports(
    current_user: User = Depends(require_roles(UserRole.MANAGER, exact_match=True))
):
    """仅经理可访问的报告"""
    # 只有经理可以访问，管理员不可以
    pass
```

### 4. 业务逻辑中的权限检查

#### 动态权限检查
```python
def business_logic(user: User, resource_id: int):
    # 检查用户是否有权限访问特定资源
    if check_user_permission(user, [UserRole.ADMIN]):
        # 管理员可以访问所有资源
        return get_all_resources()
    elif check_user_permission(user, [UserRole.MANAGER]):
        # 经理可以访问自己管理的资源
        return get_resources_by_manager(user.id)
    else:
        # 专员只能访问分配给自己的资源
        return get_resources_by_specialist(user.id)
```

#### 资源所有权检查
```python
def check_resource_access(user: User, resource: Resource):
    """检查用户是否有权限访问特定资源"""
    # 管理员可以访问所有资源
    if check_user_permission(user, [UserRole.ADMIN]):
        return True
    
    # 经理可以访问自己管理的项目下的资源
    if check_user_permission(user, [UserRole.MANAGER]):
        return resource.project.manager_id == user.id
    
    # 专员只能访问分配给自己的资源
    if check_user_permission(user, [UserRole.SPECIALIST]):
        return resource.assigned_to == user.id
    
    return False
```

### 5. 权限检查最佳实践

#### 在路由级别检查基本权限
```python
@router.get("/projects/{project_id}")
async def get_project(
    project_id: int,
    current_user: User = Depends(require_any_role())  # 基本认证
):
    """获取项目详情"""
    # 在业务逻辑中进行细粒度权限检查
    project = get_project_by_id(project_id)
    
    if not check_resource_access(current_user, project):
        raise HTTPException(
            status_code=403,
            detail="您没有权限访问此项目"
        )
    
    return project
```

#### 在服务层检查权限
```python
class ProjectService:
    @staticmethod
    def get_user_projects(user: User):
        """根据用户角色返回可访问的项目"""
        if check_user_permission(user, [UserRole.ADMIN]):
            return Project.get_all()
        elif check_user_permission(user, [UserRole.MANAGER]):
            return Project.get_by_manager(user.id)
        else:
            return Project.get_by_specialist(user.id)
```

### 6. 错误处理标准

#### 使用标准错误响应
```python
from app.core.rbac import RBACResponse

# 权限不足
raise RBACResponse.permission_denied(["admin", "manager"], user.role)

# 未认证
raise RBACResponse.unauthorized()

# 账户被禁用
raise RBACResponse.account_disabled()
```

#### 自定义权限错误
```python
def check_project_permission(user: User, project: Project):
    if not check_resource_access(user, project):
        raise HTTPException(
            status_code=403,
            detail=f"您没有权限访问项目 '{project.name}'"
        )
```

### 7. 测试要求

#### 每个端点都必须包含权限测试
```python
def test_create_project_permissions():
    """测试创建项目的权限控制"""
    # 测试管理员可以创建
    admin_response = client.post("/projects", 
                                headers={"Authorization": f"Bearer {admin_token}"},
                                json=project_data)
    assert admin_response.status_code == 200
    
    # 测试经理可以创建
    manager_response = client.post("/projects",
                                  headers={"Authorization": f"Bearer {manager_token}"},
                                  json=project_data)
    assert manager_response.status_code == 200
    
    # 测试专员不能创建
    specialist_response = client.post("/projects",
                                     headers={"Authorization": f"Bearer {specialist_token}"},
                                     json=project_data)
    assert specialist_response.status_code == 403
```

### 8. 文档要求

#### API文档必须标注权限要求
```python
@router.post("/projects")
async def create_project(
    project_data: ProjectCreate,
    current_user: User = Depends(require_manager())
):
    """
    创建新项目
    
    **权限要求**: 经理或管理员
    
    - 管理员: 可以创建任何项目
    - 经理: 可以创建自己管理的项目
    - 专员: 无权限创建项目
    """
    pass
```

### 9. 开发检查清单

在开发任何新的API端点时，必须检查：

- [ ] 是否导入了必要的RBAC模块？
- [ ] 是否在路由函数中添加了权限检查依赖？
- [ ] 是否选择了正确的权限级别？
- [ ] 是否在业务逻辑中进行了细粒度权限检查？
- [ ] 是否提供了清晰的错误信息？
- [ ] 是否编写了权限测试用例？
- [ ] 是否在API文档中标注了权限要求？

### 10. 常见权限场景

#### 项目管理API
```python
# 创建项目 - 经理权限
@router.post("/projects")
async def create_project(current_user: User = Depends(require_manager())):
    pass

# 查看项目列表 - 任何角色，但返回不同内容
@router.get("/projects")
async def list_projects(current_user: User = Depends(require_any_role())):
    return ProjectService.get_user_projects(current_user)

# 删除项目 - 仅管理员
@router.delete("/projects/{project_id}")
async def delete_project(current_user: User = Depends(require_admin())):
    pass
```

#### 文档管理API
```python
# 上传文档 - 经理权限
@router.post("/documents")
async def upload_document(current_user: User = Depends(require_manager())):
    pass

# 查看文档 - 任何角色，但需要检查访问权限
@router.get("/documents/{doc_id}")
async def get_document(
    doc_id: int,
    current_user: User = Depends(require_any_role())
):
    document = get_document_by_id(doc_id)
    if not check_resource_access(current_user, document):
        raise HTTPException(403, "无权限访问此文档")
    return document
```

#### 用户管理API
```python
# 管理用户 - 仅管理员
@router.post("/users")
async def create_user(current_user: User = Depends(require_admin())):
    pass

# 查看用户信息 - 任何角色，但只能查看自己
@router.get("/users/me")
async def get_my_info(current_user: User = Depends(require_any_role())):
    return current_user

# 查看所有用户 - 仅管理员
@router.get("/users")
async def list_users(current_user: User = Depends(require_admin())):
    pass
```

### 11. 违规处理

如果发现任何新的API端点没有实现权限控制，将被视为严重的安全漏洞，必须立即修复。

### 12. 测试用户

在开发和测试中使用以下标准测试用户：

- **admin/admin123** - 管理员测试
- **manager/manager123** - 经理测试  
- **specialist/specialist123** - 专员测试

## 实施指导

在每次开发新功能时：

1. **设计阶段**: 确定每个端点需要的权限级别
2. **编码阶段**: 实现权限检查逻辑
3. **测试阶段**: 验证权限控制是否正确
4. **文档阶段**: 记录权限要求

## 总结

RBAC不是可选的，而是强制性的安全要求。每个开发者都有责任确保自己开发的功能符合权限控制要求。这不仅是为了系统安全，也是为了提供良好的用户体验。


#!/usr/bin/env python3
"""
测试RBAC用户登录功能
验证新创建的admin、manager、specialist用户可以正常登录
"""

import sys
import os
import requests
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_user_login(username, password, expected_role):
    """测试用户登录"""
    try:
        # 登录请求
        login_data = {
            "username": username,
            "password": password
        }
        
        response = requests.post(
            f"{BASE_URL}/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            user_info = data.get("data", {})
            
            print(f"✅ {username} 登录成功")
            print(f"   角色: {user_info.get('role', 'N/A')}")
            print(f"   用户名: {user_info.get('username', 'N/A')}")
            print(f"   邮箱: {user_info.get('email', 'N/A')}")
            print(f"   Token: {user_info.get('access_token', 'N/A')[:50]}...")
            
            # 验证角色是否正确
            if user_info.get('role') == expected_role:
                print(f"   ✅ 角色验证通过")
            else:
                print(f"   ❌ 角色验证失败，期望: {expected_role}, 实际: {user_info.get('role')}")
            
            return user_info.get('access_token')
        else:
            print(f"❌ {username} 登录失败")
            print(f"   状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('detail', 'N/A')}")
            except:
                print(f"   响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ {username} 登录异常: {str(e)}")
        return None

def test_rbac_endpoint_access(token, username, role, endpoint_path, expected_success=True):
    """测试RBAC端点访问"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(
            f"{BASE_URL}{endpoint_path}",
            headers=headers,
            timeout=10
        )
        
        success = response.status_code == 200
        
        if success == expected_success:
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
        
        print(f"   {status} {username}({role}) 访问 {endpoint_path}")
        print(f"        状态码: {response.status_code} (期望: {'200' if expected_success else '403'})")
        
        if response.status_code == 200:
            try:
                data = response.json()
                message = data.get('data', {}).get('message', 'N/A')
                print(f"        响应: {message}")
            except:
                pass
        elif response.status_code == 403:
            try:
                error_data = response.json()
                print(f"        错误: {error_data.get('detail', 'N/A')}")
            except:
                pass
        
        return success
        
    except Exception as e:
        print(f"   ❌ ERROR {username} 访问 {endpoint_path} 异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("="*80)
    print("RBAC用户登录和权限测试")
    print("="*80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("注意: 需要后端服务运行在 http://localhost:8000")
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/auth/health", timeout=5)
        service_running = True
    except:
        try:
            response = requests.get("http://localhost:8000/docs", timeout=5)
            service_running = True
        except:
            service_running = False
    
    if not service_running:
        print("\n❌ 后端服务未运行，请先启动服务:")
        print("   cd backend && python main.py")
        return
    
    print("\n✅ 后端服务正在运行")
    
    # 测试用户登录
    print("\n" + "="*60)
    print("第一步: 测试用户登录")
    print("="*60)
    
    test_users = [
        ("admin", "admin123", "admin"),
        ("manager", "manager123", "manager"),
        ("specialist", "specialist123", "specialist")
    ]
    
    user_tokens = {}
    
    for username, password, expected_role in test_users:
        print(f"\n🔐 测试 {username} 登录:")
        token = test_user_login(username, password, expected_role)
        if token:
            user_tokens[username] = token
    
    # 测试RBAC端点访问
    print("\n" + "="*60)
    print("第二步: 测试RBAC端点访问")
    print("="*60)
    
    rbac_test_cases = [
        # (endpoint, admin_should_pass, manager_should_pass, specialist_should_pass)
        ("/rbac/admin-only", True, False, False),
        ("/rbac/manager-and-above", True, True, False),
        ("/rbac/specialist-and-above", True, True, True),
        ("/rbac/any-authenticated", True, True, True),
        ("/rbac/system-info", True, False, False),
    ]
    
    for endpoint, admin_pass, manager_pass, specialist_pass in rbac_test_cases:
        print(f"\n🔍 测试端点: {endpoint}")
        
        # 测试admin访问
        if "admin" in user_tokens:
            test_rbac_endpoint_access(user_tokens["admin"], "admin", "admin", endpoint, admin_pass)
        
        # 测试manager访问
        if "manager" in user_tokens:
            test_rbac_endpoint_access(user_tokens["manager"], "manager", "manager", endpoint, manager_pass)
        
        # 测试specialist访问
        if "specialist" in user_tokens:
            test_rbac_endpoint_access(user_tokens["specialist"], "specialist", "specialist", endpoint, specialist_pass)
    
    # 测试权限信息查询
    print("\n" + "="*60)
    print("第三步: 测试权限信息查询")
    print("="*60)
    
    for username, token in user_tokens.items():
        print(f"\n📋 查询 {username} 的权限信息:")
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(
                f"{BASE_URL}/rbac/me/permissions",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                permissions_data = data.get("data", {})
                
                print(f"   ✅ 权限查询成功")
                print(f"   角色: {permissions_data.get('role', 'N/A')}")
                print(f"   角色等级: {permissions_data.get('role_level', 'N/A')}")
                print(f"   权限数量: {len(permissions_data.get('permissions', []))}")
                print(f"   权限列表: {', '.join(permissions_data.get('permissions', []))}")
            else:
                print(f"   ❌ 权限查询失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 权限查询异常: {str(e)}")
    
    # 测试总结
    print("\n" + "="*80)
    print("测试总结")
    print("="*80)
    
    print(f"✅ 成功创建并测试了 {len(user_tokens)} 个用户:")
    for username in user_tokens:
        print(f"   - {username}")
    
    print(f"\n📚 测试用户信息:")
    print(f"   - admin/admin123 (管理员) - 可访问所有端点")
    print(f"   - manager/manager123 (经理) - 可访问经理及以下端点")
    print(f"   - specialist/specialist123 (专员) - 可访问专员端点")
    
    print(f"\n🎯 下一步建议:")
    print(f"   1. 在前端使用这些用户测试登录功能")
    print(f"   2. 开发项目管理API时使用RBAC权限控制")
    print(f"   3. 继续开发PAR-201任务")

if __name__ == "__main__":
    main() 
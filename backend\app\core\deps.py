"""
FastAPI依赖项
包括认证、数据库会话等依赖注入
"""

from typing import Generator, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from ..database import get_db
from ..core.security import verify_token
from ..services.user_service import UserService
from ..models.user import User, UserRole

# 导入新的RBAC模块
from ..core.rbac import (
    get_current_user_from_token,
    require_roles,
    require_admin,
    require_manager,
    require_specialist,
    require_any_role,
    get_current_user_optional,
    check_user_permission,
    get_user_permissions
)

# HTTP Bearer认证方案
security = HTTPBearer()


def get_current_user(
    db: Session = Depends(get_db),
    token: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    获取当前认证用户 (保持向后兼容)
    
    Args:
        db: 数据库会话
        token: JWT令牌
    
    Returns:
        当前用户对象
    
    Raises:
        HTTPException: 认证失败
    """
    # WHY: 重用新的RBAC模块中的用户获取逻辑
    return get_current_user_from_token(db, token)


def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    获取当前激活用户(已弃用，保持兼容性)
    
    Args:
        current_user: 当前用户
    
    Returns:
        当前激活用户
    """
    return current_user


def require_role(required_role: UserRole):
    """
    创建角色权限检查依赖项 (保持向后兼容)
    
    Args:
        required_role: 所需角色
    
    Returns:
        权限检查依赖函数
    """
    # WHY: 使用新的RBAC模块中的角色检查逻辑
    return require_roles(required_role)


# 以下函数保持向后兼容，但内部使用新的RBAC模块
def require_admin_old(current_user: User = Depends(get_current_user)) -> User:
    """
    要求管理员权限 (旧版本，保持兼容)
    
    Args:
        current_user: 当前用户
    
    Returns:
        管理员用户
    """
    if not current_user.has_permission(UserRole.ADMIN):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


def require_manager_old(current_user: User = Depends(get_current_user)) -> User:
    """
    要求经理权限 (旧版本，保持兼容)
    
    Args:
        current_user: 当前用户
    
    Returns:
        经理用户
    """
    if not current_user.has_permission(UserRole.MANAGER):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要经理权限"
        )
    return current_user


# 导出新的RBAC功能
__all__ = [
    # 向后兼容的函数
    "get_current_user",
    "get_current_active_user", 
    "require_role",
    "require_admin_old",
    "require_manager_old",
    "get_current_user_optional",
    
    # 新的RBAC功能
    "get_current_user_from_token",
    "require_roles",
    "require_admin",
    "require_manager", 
    "require_specialist",
    "require_any_role",
    "check_user_permission",
    "get_user_permissions",
] 
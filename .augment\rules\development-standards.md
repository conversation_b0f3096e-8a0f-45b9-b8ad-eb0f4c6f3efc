---
type: "always_apply"
---
# 开发标准与规范

## 编码规范

### Python后端规范
- 使用Python 3.10+，遵循PEP 8编码标准
- 使用FastAPI框架，采用异步编程模式
- 所有API接口必须包含完整的类型注解和文档字符串
- 使用Pydantic进行数据验证和序列化
- 错误处理采用统一的异常处理机制

### 前端规范
- 使用TypeScript，严格类型检查
- 采用React函数组件 + Hooks模式
- 使用Ant Design Pro组件库
- 状态管理使用Redux Toolkit或Zustand
- 所有组件必须有完整的PropTypes或TypeScript接口定义

### 数据库规范
- 使用PostgreSQL，所有表必须有主键和时间戳字段
- 外键约束必须明确定义
- 索引设计需考虑查询性能
- 敏感数据加密存储

## API设计标准

### RESTful API规范
- 使用标准HTTP状态码
- 统一的响应格式：`{"code": 200, "message": "success", "data": {...}}`
- 分页参数统一：`page`, `pageSize`, `total`
- 错误响应格式：`{"code": 400, "message": "错误描述", "details": {...}}`

### 内部服务API
- 所有内部服务API路径以`/internal/v1/`开头
- 使用统一的认证和授权机制
- 支持链路追踪和监控

## 开发流程

### 代码提交规范
- 使用语义化提交信息：`feat:`, `fix:`, `docs:`, `refactor:`
- 每次提交必须包含测试用例
- 代码审查通过后才能合并

### 测试要求
- 单元测试覆盖率不低于80%
- 集成测试覆盖核心业务流程
- 性能测试验证关键指标

### 部署流程
- 使用Docker容器化部署
- 支持蓝绿部署和回滚
- 监控和日志收集完整
#!/usr/bin/env python3
"""
测试admin用户登录
"""
import requests
import json

def test_admin_login():
    """测试admin用户登录"""
    base_url = "http://localhost:8000"
    
    # 测试admin用户登录
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("🔐 测试admin用户登录...")
    print(f"请求URL: {base_url}/api/v1/auth/login")
    print(f"请求数据: {login_data}")
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ admin用户登录成功!")
            print(f"  - 令牌类型: {result.get('token_type')}")
            print(f"  - 访问令牌: {result.get('access_token')[:50]}...")
            print(f"  - 过期时间: {result.get('expires_in')}秒")
            
            # 使用令牌获取用户信息
            token = result.get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            
            me_response = requests.get(f"{base_url}/api/v1/auth/me", headers=headers)
            if me_response.status_code == 200:
                user_info = me_response.json()
                print(f"  - 用户信息: {user_info}")
            else:
                print(f"❌ 获取用户信息失败: {me_response.text}")
                
        else:
            print(f"❌ admin用户登录失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保后端服务正在运行")
        print("请运行: python main.py")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_admin_login() 
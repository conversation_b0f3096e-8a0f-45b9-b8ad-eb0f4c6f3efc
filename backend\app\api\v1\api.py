"""
API v1版本主路由
汇总所有v1版本的API路由
"""

from fastapi import APIRouter

from .auth import router as auth_router
from .users import router as users_router
from .rbac import router as rbac_router
from .projects import router as projects_router

api_router = APIRouter()

# 认证相关路由
api_router.include_router(
    auth_router,
    prefix="/auth",
    tags=["认证"]
)

# 用户管理路由
api_router.include_router(
    users_router,
    prefix="/users",
    tags=["用户管理"]
)

# RBAC权限测试路由
api_router.include_router(
    rbac_router,
    prefix="/rbac",
    tags=["权限控制"]
)

# 项目管理路由
api_router.include_router(
    projects_router,
    prefix="/projects",
    tags=["项目管理"]
) 
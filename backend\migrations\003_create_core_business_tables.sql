-- 创建核心业务表
-- 迁移文件: 003_create_core_business_tables.sql
-- 创建时间: 2025-07-15
-- 说明: 创建招标文档、分析报告、投标书等核心业务表

-- =============================================================================
-- 1. 招标文档表 (tender_documents)
-- =============================================================================
CREATE TABLE IF NOT EXISTS tender_documents (
    id SERIAL PRIMARY KEY,
    project_id INTEGER UNIQUE NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL DEFAULT 0,
    file_type VARCHAR(50) NOT NULL,
    storage_path VARCHAR(1024) NOT NULL,
    extracted_text_path VARCHAR(1024),
    extraction_status VARCHAR(50) NOT NULL DEFAULT 'pending',
    page_count INTEGER DEFAULT 0,
    upload_by_user_id INTEGER NOT NULL,
    uploaded_at TIMESTAMPTZ DEFAULT NOW(),
    extracted_at TIMESTAMPTZ,
    
    -- 外键约束
    CONSTRAINT fk_tender_documents_project 
        FOREIGN KEY (project_id) 
        REFERENCES projects(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT fk_tender_documents_upload_by_user 
        FOREIGN KEY (upload_by_user_id) 
        REFERENCES users(id) 
        ON DELETE RESTRICT,
    
    -- 状态约束
    CONSTRAINT check_tender_document_extraction_status 
        CHECK (extraction_status IN ('pending', 'processing', 'completed', 'failed')),
    
    -- 文件类型约束
    CONSTRAINT check_tender_document_file_type 
        CHECK (file_type IN ('pdf', 'docx', 'doc', 'txt'))
);

-- =============================================================================
-- 2. 分析报告表 (analysis_reports)
-- =============================================================================
CREATE TABLE IF NOT EXISTS analysis_reports (
    id SERIAL PRIMARY KEY,
    project_id INTEGER UNIQUE NOT NULL,
    tender_document_id INTEGER NOT NULL,
    report_content JSONB NOT NULL,
    generation_status VARCHAR(50) NOT NULL DEFAULT 'pending',
    llm_model_used VARCHAR(255),
    token_usage JSONB,
    generated_by_user_id INTEGER NOT NULL,
    generated_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 外键约束
    CONSTRAINT fk_analysis_reports_project 
        FOREIGN KEY (project_id) 
        REFERENCES projects(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT fk_analysis_reports_tender_document 
        FOREIGN KEY (tender_document_id) 
        REFERENCES tender_documents(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT fk_analysis_reports_generated_by_user 
        FOREIGN KEY (generated_by_user_id) 
        REFERENCES users(id) 
        ON DELETE RESTRICT,
    
    -- 状态约束
    CONSTRAINT check_analysis_report_generation_status 
        CHECK (generation_status IN ('pending', 'processing', 'completed', 'failed'))
);

-- =============================================================================
-- 3. 投标书表 (bid_documents)
-- =============================================================================
CREATE TABLE IF NOT EXISTS bid_documents (
    id SERIAL PRIMARY KEY,
    project_id INTEGER UNIQUE NOT NULL,
    document_title VARCHAR(255) NOT NULL,
    prosemirror_content JSONB NOT NULL,
    document_version INTEGER NOT NULL DEFAULT 1,
    document_status VARCHAR(50) NOT NULL DEFAULT 'draft',
    word_count INTEGER DEFAULT 0,
    last_edited_by_user_id INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 外键约束
    CONSTRAINT fk_bid_documents_project 
        FOREIGN KEY (project_id) 
        REFERENCES projects(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT fk_bid_documents_last_edited_by_user 
        FOREIGN KEY (last_edited_by_user_id) 
        REFERENCES users(id) 
        ON DELETE RESTRICT,
    
    -- 状态约束
    CONSTRAINT check_bid_document_status 
        CHECK (document_status IN ('draft', 'reviewing', 'approved', 'submitted', 'archived'))
);

-- =============================================================================
-- 4. 投标书版本历史表 (bid_document_versions)
-- =============================================================================
CREATE TABLE IF NOT EXISTS bid_document_versions (
    id SERIAL PRIMARY KEY,
    bid_document_id INTEGER NOT NULL,
    version_number INTEGER NOT NULL,
    prosemirror_content JSONB NOT NULL,
    change_summary TEXT,
    word_count INTEGER DEFAULT 0,
    created_by_user_id INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 外键约束
    CONSTRAINT fk_bid_document_versions_bid_document 
        FOREIGN KEY (bid_document_id) 
        REFERENCES bid_documents(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT fk_bid_document_versions_created_by_user 
        FOREIGN KEY (created_by_user_id) 
        REFERENCES users(id) 
        ON DELETE RESTRICT,
    
    -- 唯一约束
    CONSTRAINT uk_bid_document_versions_document_version 
        UNIQUE (bid_document_id, version_number)
);

-- =============================================================================
-- 5. 文档导出记录表 (document_exports)
-- =============================================================================
CREATE TABLE IF NOT EXISTS document_exports (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    bid_document_id INTEGER NOT NULL,
    export_format VARCHAR(20) NOT NULL,
    export_status VARCHAR(50) NOT NULL DEFAULT 'pending',
    file_path VARCHAR(1024),
    file_size BIGINT DEFAULT 0,
    export_by_user_id INTEGER NOT NULL,
    exported_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    
    -- 外键约束
    CONSTRAINT fk_document_exports_project 
        FOREIGN KEY (project_id) 
        REFERENCES projects(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT fk_document_exports_bid_document 
        FOREIGN KEY (bid_document_id) 
        REFERENCES bid_documents(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT fk_document_exports_export_by_user 
        FOREIGN KEY (export_by_user_id) 
        REFERENCES users(id) 
        ON DELETE RESTRICT,
    
    -- 状态约束
    CONSTRAINT check_document_export_status 
        CHECK (export_status IN ('pending', 'processing', 'completed', 'failed', 'expired')),
    
    -- 格式约束
    CONSTRAINT check_document_export_format 
        CHECK (export_format IN ('pdf', 'docx', 'html'))
);

-- =============================================================================
-- 6. 项目活动日志表 (project_activity_logs)
-- =============================================================================
CREATE TABLE IF NOT EXISTS project_activity_logs (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    activity_description TEXT NOT NULL,
    activity_data JSONB,
    performed_by_user_id INTEGER NOT NULL,
    performed_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 外键约束
    CONSTRAINT fk_project_activity_logs_project 
        FOREIGN KEY (project_id) 
        REFERENCES projects(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT fk_project_activity_logs_performed_by_user 
        FOREIGN KEY (performed_by_user_id) 
        REFERENCES users(id) 
        ON DELETE RESTRICT,
    
    -- 活动类型约束
    CONSTRAINT check_project_activity_type 
        CHECK (activity_type IN (
            'project_created', 'project_updated', 'project_status_changed',
            'tender_document_uploaded', 'tender_document_extracted',
            'analysis_report_generated', 'bid_document_created', 
            'bid_document_updated', 'document_exported',
            'manager_assigned', 'project_archived'
        ))
);

-- =============================================================================
-- 创建索引
-- =============================================================================

-- 招标文档表索引
CREATE INDEX IF NOT EXISTS idx_tender_documents_project_id ON tender_documents(project_id);
CREATE INDEX IF NOT EXISTS idx_tender_documents_extraction_status ON tender_documents(extraction_status);
CREATE INDEX IF NOT EXISTS idx_tender_documents_uploaded_at ON tender_documents(uploaded_at);
CREATE INDEX IF NOT EXISTS idx_tender_documents_file_type ON tender_documents(file_type);

-- 分析报告表索引
CREATE INDEX IF NOT EXISTS idx_analysis_reports_project_id ON analysis_reports(project_id);
CREATE INDEX IF NOT EXISTS idx_analysis_reports_tender_document_id ON analysis_reports(tender_document_id);
CREATE INDEX IF NOT EXISTS idx_analysis_reports_generation_status ON analysis_reports(generation_status);
CREATE INDEX IF NOT EXISTS idx_analysis_reports_generated_at ON analysis_reports(generated_at);

-- 投标书表索引
CREATE INDEX IF NOT EXISTS idx_bid_documents_project_id ON bid_documents(project_id);
CREATE INDEX IF NOT EXISTS idx_bid_documents_status ON bid_documents(document_status);
CREATE INDEX IF NOT EXISTS idx_bid_documents_updated_at ON bid_documents(updated_at);

-- 投标书版本历史表索引
CREATE INDEX IF NOT EXISTS idx_bid_document_versions_bid_document_id ON bid_document_versions(bid_document_id);
CREATE INDEX IF NOT EXISTS idx_bid_document_versions_created_at ON bid_document_versions(created_at);

-- 文档导出记录表索引
CREATE INDEX IF NOT EXISTS idx_document_exports_project_id ON document_exports(project_id);
CREATE INDEX IF NOT EXISTS idx_document_exports_bid_document_id ON document_exports(bid_document_id);
CREATE INDEX IF NOT EXISTS idx_document_exports_status ON document_exports(export_status);
CREATE INDEX IF NOT EXISTS idx_document_exports_exported_at ON document_exports(exported_at);

-- 项目活动日志表索引
CREATE INDEX IF NOT EXISTS idx_project_activity_logs_project_id ON project_activity_logs(project_id);
CREATE INDEX IF NOT EXISTS idx_project_activity_logs_activity_type ON project_activity_logs(activity_type);
CREATE INDEX IF NOT EXISTS idx_project_activity_logs_performed_at ON project_activity_logs(performed_at);

-- =============================================================================
-- 创建更新时间触发器
-- =============================================================================

-- 分析报告表更新时间触发器
CREATE OR REPLACE FUNCTION update_analysis_reports_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_analysis_reports_updated_at
    BEFORE UPDATE ON analysis_reports
    FOR EACH ROW
    EXECUTE FUNCTION update_analysis_reports_updated_at();

-- 投标书表更新时间触发器
CREATE OR REPLACE FUNCTION update_bid_documents_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_bid_documents_updated_at
    BEFORE UPDATE ON bid_documents
    FOR EACH ROW
    EXECUTE FUNCTION update_bid_documents_updated_at();

-- =============================================================================
-- 插入测试数据
-- =============================================================================

-- 插入测试招标文档
INSERT INTO tender_documents (
    project_id, original_filename, file_size, file_type, 
    storage_path, extraction_status, page_count, upload_by_user_id
) VALUES
(1, '医院信息化建设招标文件.pdf', 2048576, 'pdf', 
 '/uploads/tender_docs/hospital_tender_2024.pdf', 'completed', 25, 2),
(2, '智慧城市监控系统招标书.docx', 1536000, 'docx', 
 '/uploads/tender_docs/smart_city_tender_2024.docx', 'completed', 18, 2),
(3, '教育云平台建设需求书.pdf', 3072000, 'pdf', 
 '/uploads/tender_docs/education_cloud_tender_2024.pdf', 'processing', 32, 2)
ON CONFLICT (project_id) DO NOTHING;

-- 插入测试分析报告
INSERT INTO analysis_reports (
    project_id, tender_document_id, report_content, generation_status,
    llm_model_used, generated_by_user_id
) VALUES
(1, 1, '{"project_info": {"name": "医院信息化建设", "budget": "500万", "duration": "6个月"}, "requirements": {"technical": ["HIS系统", "EMR系统", "PACS系统"], "commercial": ["资质要求", "业绩要求"]}}', 'completed', 'deepseek-v2', 2),
(2, 2, '{"project_info": {"name": "智慧城市监控", "budget": "800万", "duration": "8个月"}, "requirements": {"technical": ["视频监控", "AI分析", "云存储"], "commercial": ["ISO认证", "项目经验"]}}', 'completed', 'deepseek-v2', 2)
ON CONFLICT (project_id) DO NOTHING;

-- 插入测试投标书
INSERT INTO bid_documents (
    project_id, document_title, prosemirror_content, 
    document_version, document_status, word_count, last_edited_by_user_id
) VALUES
(1, '医院信息化建设投标书', '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "这是医院信息化建设投标书的内容..."}]}]}', 1, 'draft', 1500, 2),
(2, '智慧城市监控系统投标书', '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "这是智慧城市监控系统投标书的内容..."}]}]}', 1, 'draft', 2000, 2)
ON CONFLICT (project_id) DO NOTHING;

-- 插入测试活动日志
INSERT INTO project_activity_logs (
    project_id, activity_type, activity_description, performed_by_user_id
) VALUES
(1, 'project_created', '创建了医院信息化建设项目', 1),
(1, 'tender_document_uploaded', '上传了招标文件：医院信息化建设招标文件.pdf', 2),
(1, 'analysis_report_generated', '生成了项目分析报告', 2),
(2, 'project_created', '创建了智慧城市监控系统项目', 2),
(2, 'tender_document_uploaded', '上传了招标文件：智慧城市监控系统招标书.docx', 2),
(3, 'project_created', '创建了教育云平台建设项目', 1);

-- =============================================================================
-- 验证表创建
-- =============================================================================
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN (
    'tender_documents', 'analysis_reports', 'bid_documents', 
    'bid_document_versions', 'document_exports', 'project_activity_logs'
)
ORDER BY tablename;

-- 验证外键约束
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage ccu 
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_name IN (
    'tender_documents', 'analysis_reports', 'bid_documents', 
    'bid_document_versions', 'document_exports', 'project_activity_logs'
)
ORDER BY tc.table_name, tc.constraint_name; 
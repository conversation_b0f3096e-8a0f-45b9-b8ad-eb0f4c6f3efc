"""
项目模型
定义项目表的SQLAlchemy模型
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, CheckConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..database import Base


class ProjectStatus(str, Enum):
    """项目状态枚举"""
    NEW = "new"                    # 新建
    ANALYZING = "analyzing"        # 分析中
    OUTLINING = "outlining"        # 大纲生成中
    DRAFTING = "drafting"          # 起草中
    REVIEWING = "reviewing"        # 审核中
    COMPLETED = "completed"        # 已完成
    ARCHIVED = "archived"          # 已归档


class Project(Base):
    """项目模型"""
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    status = Column(String(50), nullable=False, default=ProjectStatus.NEW.value, index=True)
    
    # 关联用户
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    manager_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 项目截止时间
    deadline = Column(DateTime(timezone=True), nullable=True)
    
    # 添加状态约束
    __table_args__ = (
        CheckConstraint(
            status.in_(['new', 'analyzing', 'outlining', 'drafting', 'reviewing', 'completed', 'archived']),
            name='check_project_status'
        ),
    )

    # 关系定义
    created_by = relationship("User", foreign_keys=[created_by_user_id], back_populates="created_projects")
    manager = relationship("User", foreign_keys=[manager_id], back_populates="managed_projects")
    
    # 业务文档关系
    tender_document = relationship("TenderDocument", back_populates="project", uselist=False, cascade="all, delete-orphan")
    analysis_report = relationship("AnalysisReport", back_populates="project", uselist=False, cascade="all, delete-orphan")
    bid_document = relationship("BidDocument", back_populates="project", uselist=False, cascade="all, delete-orphan")
    
    # 活动日志关系
    activity_logs = relationship("ProjectActivityLog", back_populates="project", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', status='{self.status}')>"

    def dict(self):
        """转换为字典格式，用于API响应"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status,
            "created_by_user_id": self.created_by_user_id,
            "manager_id": self.manager_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deadline": self.deadline.isoformat() if self.deadline else None,
        }

    def can_be_accessed_by(self, user) -> bool:
        """检查用户是否可以访问此项目"""
        from .user import UserRole
        
        # 管理员可以访问所有项目
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 经理可以访问自己管理的项目和自己创建的项目
        if user.role == UserRole.MANAGER.value:
            return (self.manager_id == user.id or 
                   self.created_by_user_id == user.id)
        
        # 专员只能访问分配给自己的项目
        if user.role == UserRole.SPECIALIST.value:
            # TODO: 实现专员项目分配关系
            return self.created_by_user_id == user.id
        
        return False

    def can_be_edited_by(self, user) -> bool:
        """检查用户是否可以编辑此项目"""
        from .user import UserRole
        
        # 管理员可以编辑所有项目
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 经理可以编辑自己管理的项目
        if user.role == UserRole.MANAGER.value:
            return self.manager_id == user.id or self.created_by_user_id == user.id
        
        # 专员不能编辑项目基本信息
        return False

    def can_be_deleted_by(self, user) -> bool:
        """检查用户是否可以删除此项目"""
        from .user import UserRole
        
        # 只有管理员可以删除项目
        return user.role == UserRole.ADMIN.value

    def get_progress_percentage(self) -> int:
        """获取项目进度百分比"""
        status_progress = {
            ProjectStatus.NEW.value: 0,
            ProjectStatus.ANALYZING.value: 20,
            ProjectStatus.OUTLINING.value: 40,
            ProjectStatus.DRAFTING.value: 60,
            ProjectStatus.REVIEWING.value: 80,
            ProjectStatus.COMPLETED.value: 100,
            ProjectStatus.ARCHIVED.value: 100,
        }
        return status_progress.get(self.status, 0)

    def get_status_display(self) -> str:
        """获取状态显示名称"""
        status_display = {
            ProjectStatus.NEW.value: "新建",
            ProjectStatus.ANALYZING.value: "分析中",
            ProjectStatus.OUTLINING.value: "大纲生成中",
            ProjectStatus.DRAFTING.value: "起草中",
            ProjectStatus.REVIEWING.value: "审核中",
            ProjectStatus.COMPLETED.value: "已完成",
            ProjectStatus.ARCHIVED.value: "已归档",
        }
        return status_display.get(self.status, "未知")

    def get_status_color(self) -> str:
        """获取状态颜色（用于UI显示）"""
        status_colors = {
            ProjectStatus.NEW.value: "default",
            ProjectStatus.ANALYZING.value: "processing",
            ProjectStatus.OUTLINING.value: "warning",
            ProjectStatus.DRAFTING.value: "cyan",
            ProjectStatus.REVIEWING.value: "orange",
            ProjectStatus.COMPLETED.value: "success",
            ProjectStatus.ARCHIVED.value: "gray",
        }
        return status_colors.get(self.status, "default")

    def is_active(self) -> bool:
        """检查项目是否处于活跃状态"""
        return self.status not in [ProjectStatus.COMPLETED.value, ProjectStatus.ARCHIVED.value]

    def is_completed(self) -> bool:
        """检查项目是否已完成"""
        return self.status == ProjectStatus.COMPLETED.value

    def is_archived(self) -> bool:
        """检查项目是否已归档"""
        return self.status == ProjectStatus.ARCHIVED.value

    def has_tender_document(self) -> bool:
        """检查是否有招标文档"""
        return hasattr(self, 'tender_document') and self.tender_document is not None

    def has_analysis_report(self) -> bool:
        """检查是否有分析报告"""
        return hasattr(self, 'analysis_report') and self.analysis_report is not None

    def has_bid_document(self) -> bool:
        """检查是否有投标书"""
        return hasattr(self, 'bid_document') and self.bid_document is not None

    def get_next_status(self) -> str:
        """获取下一个状态"""
        status_flow = {
            ProjectStatus.NEW.value: ProjectStatus.ANALYZING.value,
            ProjectStatus.ANALYZING.value: ProjectStatus.OUTLINING.value,
            ProjectStatus.OUTLINING.value: ProjectStatus.DRAFTING.value,
            ProjectStatus.DRAFTING.value: ProjectStatus.REVIEWING.value,
            ProjectStatus.REVIEWING.value: ProjectStatus.COMPLETED.value,
            ProjectStatus.COMPLETED.value: ProjectStatus.ARCHIVED.value,
        }
        return status_flow.get(self.status, self.status)

    def can_advance_to_next_status(self) -> bool:
        """检查是否可以推进到下一个状态"""
        # 根据业务逻辑检查是否可以推进
        if self.status == ProjectStatus.NEW.value:
            return True  # 新建项目可以开始分析
        elif self.status == ProjectStatus.ANALYZING.value:
            return self.has_tender_document() and self.has_analysis_report()
        elif self.status == ProjectStatus.OUTLINING.value:
            return self.has_analysis_report()
        elif self.status == ProjectStatus.DRAFTING.value:
            return self.has_bid_document()
        elif self.status == ProjectStatus.REVIEWING.value:
            return self.has_bid_document()
        elif self.status == ProjectStatus.COMPLETED.value:
            return True  # 已完成项目可以归档
        else:
            return False

    def get_days_since_created(self) -> int:
        """获取创建天数"""
        if self.created_at:
            return (datetime.utcnow() - self.created_at.replace(tzinfo=None)).days
        return 0

    def get_days_until_deadline(self) -> int:
        """获取距离截止日期的天数"""
        if self.deadline:
            return (self.deadline.replace(tzinfo=None) - datetime.utcnow()).days
        return 0

    def is_overdue(self) -> bool:
        """检查是否已过期"""
        if self.deadline and not self.is_completed():
            return datetime.utcnow() > self.deadline.replace(tzinfo=None)
        return False

    def get_summary(self) -> dict:
        """获取项目摘要信息"""
        return {
            "id": self.id,
            "name": self.name,
            "status": self.status,
            "status_display": self.get_status_display(),
            "progress": self.get_progress_percentage(),
            "is_active": self.is_active(),
            "is_overdue": self.is_overdue(),
            "days_since_created": self.get_days_since_created(),
            "has_tender_document": self.has_tender_document(),
            "has_analysis_report": self.has_analysis_report(),
            "has_bid_document": self.has_bid_document(),
        } 
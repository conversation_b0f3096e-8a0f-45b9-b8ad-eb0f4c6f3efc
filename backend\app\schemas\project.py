"""
项目相关的Pydantic Schema
用于API请求和响应的数据验证
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, validator

from ..models.project import ProjectStatus


class ProjectBase(BaseModel):
    """项目基础Schema"""
    name: str = Field(..., min_length=1, max_length=255, description="项目名称")
    description: Optional[str] = Field(None, description="项目描述")
    deadline: Optional[datetime] = Field(None, description="项目截止时间")


class ProjectCreate(ProjectBase):
    """创建项目Schema"""
    manager_id: Optional[int] = Field(None, description="项目经理ID")

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('项目名称不能为空')
        return v.strip()


class ProjectUpdate(BaseModel):
    """更新项目Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="项目名称")
    description: Optional[str] = Field(None, description="项目描述")
    status: Optional[ProjectStatus] = Field(None, description="项目状态")
    manager_id: Optional[int] = Field(None, description="项目经理ID")
    deadline: Optional[datetime] = Field(None, description="项目截止时间")

    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('项目名称不能为空')
        return v.strip() if v else v


class ProjectResponse(ProjectBase):
    """项目响应Schema"""
    id: int
    status: ProjectStatus
    created_by_user_id: int
    manager_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ProjectListResponse(BaseModel):
    """项目列表响应Schema"""
    id: int
    name: str
    description: Optional[str] = None
    status: ProjectStatus
    created_by_user_id: int
    manager_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    deadline: Optional[datetime] = None

    class Config:
        from_attributes = True


class ProjectDetailResponse(ProjectResponse):
    """项目详情响应Schema"""
    # 可以在这里添加更多详细信息，如关联的文档、任务等
    pass


class ProjectStatusUpdate(BaseModel):
    """项目状态更新Schema"""
    status: ProjectStatus

    @validator('status')
    def validate_status_transition(cls, v):
        # 可以在这里添加状态转换验证逻辑
        return v


class ProjectAssignManager(BaseModel):
    """分配项目经理Schema"""
    manager_id: int

    @validator('manager_id')
    def validate_manager_id(cls, v):
        if v <= 0:
            raise ValueError('经理ID必须大于0')
        return v


class ProjectQuery(BaseModel):
    """项目查询Schema"""
    name: Optional[str] = Field(None, description="项目名称搜索")
    status: Optional[ProjectStatus] = Field(None, description="项目状态过滤")
    created_by_user_id: Optional[int] = Field(None, description="创建者ID过滤")
    manager_id: Optional[int] = Field(None, description="经理ID过滤")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")
    
    class Config:
        from_attributes = True 
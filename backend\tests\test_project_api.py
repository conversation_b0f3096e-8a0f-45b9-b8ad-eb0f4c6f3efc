"""
项目API测试
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime

from app.main import app
from app.database import get_db
from app.models.user import User, UserRole
from app.models.project import Project, ProjectStatus
from app.core.security import create_access_token

client = TestClient(app)


class TestProjectAPI:
    """测试项目API"""

    def test_create_project_with_manager(self):
        """测试经理创建项目"""
        # 创建测试令牌
        manager_token = create_access_token("manager")
        
        project_data = {
            "name": "测试项目",
            "description": "这是一个测试项目",
            "manager_id": 2  # 假设ID为2的用户是经理
        }
        
        response = client.post(
            "/api/v1/projects/",
            json=project_data,
            headers={"Authorization": f"Bearer {manager_token}"}
        )
        
        # 注意：这个测试需要实际的数据库连接才能通过
        # 在实际环境中，需要先设置测试数据库
        print(f"创建项目响应状态码: {response.status_code}")
        if response.status_code != 200:
            print(f"响应内容: {response.text}")

    def test_create_project_with_specialist_should_fail(self):
        """测试专员创建项目应该失败"""
        specialist_token = create_access_token("specialist")
        
        project_data = {
            "name": "测试项目",
            "description": "这是一个测试项目"
        }
        
        response = client.post(
            "/api/v1/projects/",
            json=project_data,
            headers={"Authorization": f"Bearer {specialist_token}"}
        )
        
        # 应该返回403权限不足
        print(f"专员创建项目响应状态码: {response.status_code}")
        if response.status_code == 403:
            print("✅ 专员创建项目正确被拒绝")
        else:
            print(f"❌ 期望403，实际: {response.status_code}")

    def test_list_projects(self):
        """测试获取项目列表"""
        admin_token = create_access_token("admin")
        
        response = client.get(
            "/api/v1/projects/",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        print(f"获取项目列表响应状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 获取项目列表成功")
        else:
            print(f"响应内容: {response.text}")

    def test_get_project_statistics(self):
        """测试获取项目统计"""
        admin_token = create_access_token("admin")
        
        response = client.get(
            "/api/v1/projects/statistics/overview",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        print(f"获取项目统计响应状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 获取项目统计成功")
        else:
            print(f"响应内容: {response.text}")

    def test_unauthorized_access(self):
        """测试未认证访问"""
        response = client.get("/api/v1/projects/")
        
        # 应该返回401未认证
        assert response.status_code == 401
        print("✅ 未认证访问正确被拒绝")


class TestProjectModel:
    """测试项目模型"""

    def test_project_status_enum(self):
        """测试项目状态枚举"""
        assert ProjectStatus.NEW == "new"
        assert ProjectStatus.ANALYZING == "analyzing"
        assert ProjectStatus.OUTLINING == "outlining"
        assert ProjectStatus.DRAFTING == "drafting"
        assert ProjectStatus.REVIEWING == "reviewing"
        assert ProjectStatus.COMPLETED == "completed"
        assert ProjectStatus.ARCHIVED == "archived"
        print("✅ 项目状态枚举测试通过")

    def test_project_permissions(self):
        """测试项目权限检查"""
        # 创建测试用户
        admin_user = User(
            username="admin_test",
            email="<EMAIL>",
            hashed_password="hashed",
            role=UserRole.ADMIN.value,
            is_active=True
        )
        
        manager_user = User(
            id=2,
            username="manager_test",
            email="<EMAIL>",
            hashed_password="hashed",
            role=UserRole.MANAGER.value,
            is_active=True
        )
        
        specialist_user = User(
            id=3,
            username="specialist_test",
            email="<EMAIL>",
            hashed_password="hashed",
            role=UserRole.SPECIALIST.value,
            is_active=True
        )
        
        # 创建测试项目
        project = Project(
            id=1,
            name="测试项目",
            description="测试项目描述",
            status=ProjectStatus.NEW.value,
            created_by_user_id=1,
            manager_id=2
        )
        
        # 测试访问权限
        assert project.can_be_accessed_by(admin_user) == True
        assert project.can_be_accessed_by(manager_user) == True
        assert project.can_be_accessed_by(specialist_user) == False
        
        # 测试编辑权限
        assert project.can_be_edited_by(admin_user) == True
        assert project.can_be_edited_by(manager_user) == True
        assert project.can_be_edited_by(specialist_user) == False
        
        # 测试删除权限
        assert project.can_be_deleted_by(admin_user) == True
        assert project.can_be_deleted_by(manager_user) == False
        assert project.can_be_deleted_by(specialist_user) == False
        
        print("✅ 项目权限检查测试通过")


def test_project_schema_validation():
    """测试项目Schema验证"""
    from app.schemas.project import ProjectCreate, ProjectUpdate
    
    # 测试创建项目Schema
    valid_data = {
        "name": "测试项目",
        "description": "测试项目描述",
        "manager_id": 1
    }
    
    project_create = ProjectCreate(**valid_data)
    assert project_create.name == "测试项目"
    assert project_create.description == "测试项目描述"
    assert project_create.manager_id == 1
    
    # 测试空名称验证
    try:
        ProjectCreate(name="", description="测试")
        assert False, "应该抛出验证错误"
    except ValueError:
        pass  # 期望的行为
    
    # 测试更新项目Schema
    update_data = {
        "name": "更新后的项目名称",
        "status": ProjectStatus.ANALYZING
    }
    
    project_update = ProjectUpdate(**update_data)
    assert project_update.name == "更新后的项目名称"
    assert project_update.status == ProjectStatus.ANALYZING
    
    print("✅ 项目Schema验证测试通过")


# --- Suggested Test ---
def test_project_api_integration():
    """
    集成测试：验证项目API的完整流程
    """
    # 这个测试需要实际的数据库连接
    # 在实际环境中运行时，需要设置测试数据库
    
    # 1. 创建项目
    # 2. 获取项目列表
    # 3. 更新项目
    # 4. 删除项目
    
    print("⚠️  集成测试需要数据库连接，请在实际环境中运行")


if __name__ == "__main__":
    # 运行基本测试
    test_project_schema_validation()
    
    model_test = TestProjectModel()
    model_test.test_project_status_enum()
    model_test.test_project_permissions()
    
    api_test = TestProjectAPI()
    api_test.test_unauthorized_access()
    
    print("\n🎉 基本测试完成！")
    print("注意：完整的API测试需要数据库连接") 
---
description: 
globs: 
alwaysApply: true
---
# 开发标准与规范

## 编码规范

### Python后端规范
- 使用Python 3.10+，遵循PEP 8编码标准
- 使用FastAPI框架，采用异步编程模式
- 所有API接口必须包含完整的类型注解和文档字符串
- 使用Pydantic进行数据验证和序列化
- 错误处理采用统一的异常处理机制

### 前端规范
- 使用TypeScript，严格类型检查
- 采用React函数组件 + Hooks模式
- 使用Ant Design Pro组件库
- 状态管理使用Redux Toolkit或Zustand
- 所有组件必须有完整的PropTypes或TypeScript接口定义

### 数据库规范
- 使用PostgreSQL，所有表必须有主键和时间戳字段
- 外键约束必须明确定义
- 索引设计需考虑查询性能
- 敏感数据加密存储

## API设计标准

### RESTful API规范
- 使用标准HTTP状态码
- 统一的响应格式：`{"code": 200, "message": "success", "data": {...}}`
- 分页参数统一：`page`, `pageSize`, `total`
- 错误响应格式：`{"code": 400, "message": "错误描述", "details": {...}}`

### 内部服务API
- 所有内部服务API路径以`/internal/v1/`开头
- 使用统一的认证和授权机制
- 支持链路追踪和监控

## 开发流程

### 任务优先级
参考 [开发任务清单.md](mdc:docs/开发任务清单.md) 中的优先级定义：
- **P0 (Critical)**: 关键路径任务，无此功能则核心流程不通
- **P1 (High)**: 核心功能，构成MVP的必要部分
- **P2 (Medium)**: 重要功能，提升用户体验
- **P3 (Low)**: 优化项或高级功能

### 复杂度评估
- **S (Small)**: 1-2天
- **M (Medium)**: 3-5天
- **L (Large)**: 5-10天
- **XL (Extra Large)**: >10天 (需要进一步拆分)

### 代码审查要点
- 功能完整性和正确性
- 代码可读性和维护性
- 性能和安全性考虑
- 测试覆盖率
- 文档完整性

## 测试标准
- 单元测试覆盖率不低于80%
- 集成测试覆盖核心业务流程
- 性能测试关注关键指标：
  - 文件解析(100页内)：30秒内完成
  - 分析报告生成：2分钟内完成
  - 章节内容生成：10秒内完成
  - 文档导出(100页内)：1分钟内完成



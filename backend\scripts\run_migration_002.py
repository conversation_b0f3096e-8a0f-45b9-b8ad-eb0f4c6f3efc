#!/usr/bin/env python3
"""
运行002_create_projects_table.sql迁移脚本
"""

import sys
import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_migration():
    """运行项目表迁移"""
    print("开始运行项目表迁移...")
    
    # 数据库连接参数
    db_params = {
        'host': 'localhost',
        'database': 'intellibid',
        'user': 'postgres',
        'password': 'postgres',
        'port': 5432
    }
    
    try:
        # 连接数据库
        conn = psycopg2.connect(**db_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 读取迁移脚本
        migration_file = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), 
            'migrations', 
            '002_create_projects_table.sql'
        )
        
        with open(migration_file, 'r', encoding='utf-8') as f:
            migration_sql = f.read()
        
        # 执行迁移
        print("执行迁移脚本...")
        cursor.execute(migration_sql)
        
        print("✅ 项目表迁移完成!")
        
        # 验证表是否创建成功
        cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'projects'")
        table_count = cursor.fetchone()[0]
        
        if table_count > 0:
            print("✅ 项目表创建成功!")
            
            # 查看表结构
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'projects' 
                ORDER BY ordinal_position
            """)
            
            columns = cursor.fetchall()
            print("\n📋 项目表结构:")
            print("-" * 60)
            print(f"{'列名':<20} {'数据类型':<15} {'可空':<8} {'默认值'}")
            print("-" * 60)
            for col in columns:
                nullable = "是" if col[2] == "YES" else "否"
                default = col[3] if col[3] else ""
                print(f"{col[0]:<20} {col[1]:<15} {nullable:<8} {default}")
            
            # 查看测试数据
            cursor.execute("SELECT COUNT(*) FROM projects")
            project_count = cursor.fetchone()[0]
            print(f"\n📊 项目表中有 {project_count} 条测试数据")
            
            if project_count > 0:
                cursor.execute("SELECT id, name, status, created_by_user_id, manager_id FROM projects")
                projects = cursor.fetchall()
                print("\n🗂️  测试项目数据:")
                print("-" * 80)
                print(f"{'ID':<4} {'项目名称':<20} {'状态':<12} {'创建者ID':<8} {'经理ID'}")
                print("-" * 80)
                for project in projects:
                    print(f"{project[0]:<4} {project[1]:<20} {project[2]:<12} {project[3]:<8} {project[4] or 'NULL'}")
        else:
            print("❌ 项目表创建失败!")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_migration() 
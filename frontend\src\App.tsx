import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'

import AppLayout from '@/components/Layout/AppLayout'
import ProtectedRoute from '@/components/Auth/ProtectedRoute'
import LoginPage from '@/pages/Auth/LoginPage'
import RegisterPage from '@/pages/Auth/RegisterPage'
import DashboardPage from '@/pages/Dashboard/DashboardPage'
import ProjectListPage from '@/pages/Project/ProjectListPage'
import ProjectDetailPage from '@/pages/Project/ProjectDetailPage'
import ProjectCreatePage from '@/pages/Project/ProjectCreatePage'
import NotFoundPage from '@/pages/Error/NotFoundPage'

const App: React.FC = () => {
  return (
    <div className="app">
      <Routes>
        {/* 认证相关路由 */}
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />
        
        {/* 主应用路由 - 需要认证 */}
        <Route path="/" element={
          <ProtectedRoute>
            <AppLayout />
          </ProtectedRoute>
        }>
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="projects" element={<ProjectListPage />} />
          <Route path="projects/create" element={<ProjectCreatePage />} />
          <Route path="projects/:projectId" element={<ProjectDetailPage />} />
        </Route>
        
        {/* 404页面 */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </div>
  )
}

export default App 
import React, { useState, useEffect } from 'react';
import {
  Card,
  Typo<PERSON>,
  Button,
  Space,
  Tag,
  Row,
  Col,
  Descriptions,
  Progress,
  Timeline,
  Tabs,
  Alert,
  Spin,
  message,
  Statistic,
  Empty,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  ProjectOutlined,
  UserOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  Bar<PERSON>hartOutlined,
  HistoryOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { projectAPI, Project, User } from '../../services/api';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 项目状态配置
const PROJECT_STATUS_CONFIG = {
  new: { label: '新建', color: 'default' },
  analyzing: { label: '分析中', color: 'processing' },
  outlining: { label: '大纲生成中', color: 'warning' },
  drafting: { label: '起草中', color: 'cyan' },
  reviewing: { label: '审核中', color: 'orange' },
  completed: { label: '已完成', color: 'success' },
  archived: { label: '已归档', color: 'default' },
};

// 获取项目进度百分比
const getProgressPercentage = (status: string): number => {
  const progressMap: Record<string, number> = {
    new: 0,
    analyzing: 20,
    outlining: 40,
    drafting: 60,
    reviewing: 80,
    completed: 100,
    archived: 100,
  };
  return progressMap[status] || 0;
};

const ProjectDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { projectId } = useParams<{ projectId: string }>();
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState<Project | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // 获取当前用户信息
  const getCurrentUser = (): User | null => {
    const userInfo = localStorage.getItem('user_info');
    return userInfo ? JSON.parse(userInfo) : null;
  };

  const currentUser = getCurrentUser();

  // 获取项目详情
  const fetchProjectDetail = async () => {
    if (!projectId) return;

    try {
      setLoading(true);
      const response = await projectAPI.getProject(parseInt(projectId));
      const result = response.data;

      if (result.code === 200) {
        setProject(result.data);
      } else {
        message.error(result.message || '获取项目详情失败');
      }
    } catch (error) {
      console.error('获取项目详情失败:', error);
      message.error('获取项目详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 检查编辑权限
  const canEdit = () => {
    if (!currentUser || !project) return false;
    
    return currentUser.role === 'admin' || 
           (currentUser.role === 'manager' && 
            (project.manager_id === currentUser.id || project.created_by_user_id === currentUser.id));
  };

  // 渲染项目概览
  const renderOverview = () => {
    if (!project) return null;

    const statusConfig = PROJECT_STATUS_CONFIG[project.status as keyof typeof PROJECT_STATUS_CONFIG];
    const progress = getProgressPercentage(project.status);
    const isOverdue = project.deadline && dayjs().isAfter(dayjs(project.deadline));

    return (
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 项目基本信息 */}
        <Card title="项目信息" extra={
          canEdit() && (
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => navigate(`/projects/${projectId}/edit`)}
            >
              编辑项目
            </Button>
          )
        }>
          <Row gutter={24}>
            <Col span={12}>
              <Descriptions column={1} labelStyle={{ width: '120px' }}>
                <Descriptions.Item label="项目名称">
                  <Text strong>{project.name}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="项目描述">
                  {project.description || '无描述'}
                </Descriptions.Item>
                <Descriptions.Item label="项目状态">
                  <Space>
                    <Tag color={statusConfig?.color || 'default'}>
                      {statusConfig?.label || project.status}
                    </Tag>
                    <Text type="secondary">({progress}%)</Text>
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="项目进度">
                  <Progress percent={progress} status={progress === 100 ? 'success' : 'active'} />
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={12}>
              <Descriptions column={1} labelStyle={{ width: '120px' }}>
                <Descriptions.Item label="创建者">
                  <Space>
                    <UserOutlined />
                    用户{project.created_by_user_id}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="项目经理">
                  {project.manager_id ? (
                    <Space>
                      <UserOutlined />
                      用户{project.manager_id}
                    </Space>
                  ) : (
                    <Text type="secondary">未分配</Text>
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  <Space>
                    <CalendarOutlined />
                    {dayjs(project.created_at).format('YYYY-MM-DD HH:mm')}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="截止时间">
                  {project.deadline ? (
                    <Space>
                      <ClockCircleOutlined />
                      <Text type={isOverdue ? 'danger' : undefined}>
                        {dayjs(project.deadline).format('YYYY-MM-DD HH:mm')}
                      </Text>
                      {isOverdue && <Tag color="red">已过期</Tag>}
                    </Space>
                  ) : (
                    <Text type="secondary">无截止时间</Text>
                  )}
                </Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
        </Card>

        {/* 项目统计 */}
        <Card title="项目统计">
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="项目天数"
                value={dayjs().diff(dayjs(project.created_at), 'day')}
                suffix="天"
                prefix={<CalendarOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="剩余天数"
                value={project.deadline ? dayjs(project.deadline).diff(dayjs(), 'day') : 0}
                suffix="天"
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: isOverdue ? '#ff4d4f' : undefined }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="完成进度"
                value={progress}
                suffix="%"
                prefix={<BarChartOutlined />}
                valueStyle={{ color: progress === 100 ? '#52c41a' : '#1890ff' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="最后更新"
                value={dayjs(project.updated_at).fromNow()}
                prefix={<HistoryOutlined />}
              />
            </Col>
          </Row>
        </Card>

        {/* 项目状态流程 */}
        <Card title="项目流程">
          <Timeline>
            <Timeline.Item
              color={['new', 'analyzing', 'outlining', 'drafting', 'reviewing', 'completed', 'archived'].includes(project.status) ? 'green' : 'gray'}
            >
              <Text strong>项目创建</Text>
              <div style={{ color: '#666' }}>
                {dayjs(project.created_at).format('YYYY-MM-DD HH:mm')}
              </div>
            </Timeline.Item>
            <Timeline.Item
              color={['analyzing', 'outlining', 'drafting', 'reviewing', 'completed', 'archived'].includes(project.status) ? 'green' : 'gray'}
            >
              <Text strong>开始分析</Text>
              <div style={{ color: '#666' }}>
                {project.status === 'analyzing' ? '进行中' : project.status === 'new' ? '待开始' : '已完成'}
              </div>
            </Timeline.Item>
            <Timeline.Item
              color={['outlining', 'drafting', 'reviewing', 'completed', 'archived'].includes(project.status) ? 'green' : 'gray'}
            >
              <Text strong>生成大纲</Text>
              <div style={{ color: '#666' }}>
                {project.status === 'outlining' ? '进行中' : ['new', 'analyzing'].includes(project.status) ? '待开始' : '已完成'}
              </div>
            </Timeline.Item>
            <Timeline.Item
              color={['drafting', 'reviewing', 'completed', 'archived'].includes(project.status) ? 'green' : 'gray'}
            >
              <Text strong>起草文档</Text>
              <div style={{ color: '#666' }}>
                {project.status === 'drafting' ? '进行中' : ['new', 'analyzing', 'outlining'].includes(project.status) ? '待开始' : '已完成'}
              </div>
            </Timeline.Item>
            <Timeline.Item
              color={['reviewing', 'completed', 'archived'].includes(project.status) ? 'green' : 'gray'}
            >
              <Text strong>审核文档</Text>
              <div style={{ color: '#666' }}>
                {project.status === 'reviewing' ? '进行中' : ['new', 'analyzing', 'outlining', 'drafting'].includes(project.status) ? '待开始' : '已完成'}
              </div>
            </Timeline.Item>
            <Timeline.Item
              color={['completed', 'archived'].includes(project.status) ? 'green' : 'gray'}
            >
              <Text strong>项目完成</Text>
              <div style={{ color: '#666' }}>
                {project.status === 'completed' ? '已完成' : project.status === 'archived' ? '已归档' : '待完成'}
              </div>
            </Timeline.Item>
          </Timeline>
        </Card>
      </Space>
    );
  };

  // 渲染文档管理
  const renderDocuments = () => (
    <Card title="文档管理">
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="文档管理功能开发中"
      >
        <Button type="primary">上传招标文档</Button>
      </Empty>
    </Card>
  );

  // 渲染分析报告
  const renderAnalysis = () => (
    <Card title="分析报告">
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="分析报告功能开发中"
      >
        <Button type="primary">生成分析报告</Button>
      </Empty>
    </Card>
  );

  // 渲染投标书
  const renderBidDocument = () => (
    <Card title="投标书">
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="投标书编辑功能开发中"
      >
        <Button type="primary">创建投标书</Button>
      </Empty>
    </Card>
  );

  // 渲染项目设置
  const renderSettings = () => (
    <Card title="项目设置">
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="项目设置功能开发中"
      >
        <Button type="primary">配置项目</Button>
      </Empty>
    </Card>
  );

  // 初始化数据
  useEffect(() => {
    fetchProjectDetail();
  }, [projectId]);

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!project) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="项目不存在"
          description="请检查项目ID是否正确"
          type="error"
          showIcon
        />
      </div>
    );
  }

  const statusConfig = PROJECT_STATUS_CONFIG[project.status as keyof typeof PROJECT_STATUS_CONFIG];

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Space align="center">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/projects')}
          >
            返回项目列表
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            <ProjectOutlined style={{ marginRight: 8 }} />
            {project.name}
          </Title>
          <Tag color={statusConfig?.color || 'default'}>
            {statusConfig?.label || project.status}
          </Tag>
        </Space>
      </div>

      {/* 标签页内容 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="项目概览" key="overview" icon={<ProjectOutlined />}>
          {renderOverview()}
        </TabPane>
        <TabPane tab="文档管理" key="documents" icon={<FileTextOutlined />}>
          {renderDocuments()}
        </TabPane>
        <TabPane tab="分析报告" key="analysis" icon={<BarChartOutlined />}>
          {renderAnalysis()}
        </TabPane>
        <TabPane tab="投标书" key="bid" icon={<FileTextOutlined />}>
          {renderBidDocument()}
        </TabPane>
        {canEdit() && (
          <TabPane tab="项目设置" key="settings" icon={<SettingOutlined />}>
            {renderSettings()}
          </TabPane>
        )}
      </Tabs>
    </div>
  );
};

export default ProjectDetailPage; 
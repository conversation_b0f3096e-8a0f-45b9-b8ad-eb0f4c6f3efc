"""
投标书模型
定义投标书表的SQLAlchemy模型
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, CheckConstraint, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB

from ..database import Base


class DocumentStatus(str, Enum):
    """文档状态枚举"""
    DRAFT = "draft"           # 草稿
    REVIEWING = "reviewing"   # 审核中
    APPROVED = "approved"     # 已批准
    SUBMITTED = "submitted"   # 已提交
    ARCHIVED = "archived"     # 已归档


class BidDocument(Base):
    """投标书模型"""
    __tablename__ = "bid_documents"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), unique=True, nullable=False, index=True)
    document_title = Column(String(255), nullable=False)
    prosemirror_content = Column(JSONB, nullable=False)
    document_version = Column(Integer, nullable=False, default=1)
    document_status = Column(String(50), nullable=False, default=DocumentStatus.DRAFT.value, index=True)
    word_count = Column(Integer, default=0)
    last_edited_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), index=True)

    # 添加约束
    __table_args__ = (
        CheckConstraint(
            document_status.in_(['draft', 'reviewing', 'approved', 'submitted', 'archived']),
            name='check_bid_document_status'
        ),
    )

    # 关系定义
    project = relationship("Project", back_populates="bid_document")
    last_edited_by = relationship("User", foreign_keys=[last_edited_by_user_id])
    versions = relationship("BidDocumentVersion", back_populates="bid_document", cascade="all, delete-orphan")
    exports = relationship("DocumentExport", back_populates="bid_document", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<BidDocument(id={self.id}, title='{self.document_title}', status='{self.document_status}')>"

    def dict(self):
        """转换为字典格式，用于API响应"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "document_title": self.document_title,
            "prosemirror_content": self.prosemirror_content,
            "document_version": self.document_version,
            "document_status": self.document_status,
            "word_count": self.word_count,
            "last_edited_by_user_id": self.last_edited_by_user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def can_be_accessed_by(self, user) -> bool:
        """检查用户是否可以访问此投标书"""
        from .user import UserRole
        
        # 管理员可以访问所有投标书
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 通过项目权限检查
        if hasattr(self, 'project') and self.project:
            return self.project.can_be_accessed_by(user)
        
        return False

    def can_be_edited_by(self, user) -> bool:
        """检查用户是否可以编辑此投标书"""
        from .user import UserRole
        
        # 管理员可以编辑所有投标书
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 已提交或归档的投标书不能编辑
        if self.document_status in [DocumentStatus.SUBMITTED.value, DocumentStatus.ARCHIVED.value]:
            return False
        
        # 通过项目权限检查
        if hasattr(self, 'project') and self.project:
            return self.project.can_be_edited_by(user)
        
        return False

    def can_be_deleted_by(self, user) -> bool:
        """检查用户是否可以删除此投标书"""
        from .user import UserRole
        
        # 只有管理员可以删除投标书
        return user.role == UserRole.ADMIN.value

    def can_be_submitted_by(self, user) -> bool:
        """检查用户是否可以提交此投标书"""
        from .user import UserRole
        
        # 只有已批准的投标书可以提交
        if self.document_status != DocumentStatus.APPROVED.value:
            return False
        
        # 管理员可以提交任何投标书
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 项目经理可以提交自己管理的项目的投标书
        if hasattr(self, 'project') and self.project:
            return self.project.can_be_edited_by(user)
        
        return False

    def is_draft(self) -> bool:
        """检查是否为草稿状态"""
        return self.document_status == DocumentStatus.DRAFT.value

    def is_submitted(self) -> bool:
        """检查是否已提交"""
        return self.document_status == DocumentStatus.SUBMITTED.value

    def is_editable(self) -> bool:
        """检查是否可编辑"""
        return self.document_status in [DocumentStatus.DRAFT.value, DocumentStatus.REVIEWING.value]

    def get_chapter_count(self) -> int:
        """获取章节数量"""
        if self.prosemirror_content and isinstance(self.prosemirror_content, dict):
            content = self.prosemirror_content.get('content', [])
            chapter_count = 0
            for node in content:
                if node.get('type') == 'chapter':
                    chapter_count += 1
            return chapter_count
        return 0

    def get_estimated_pages(self) -> int:
        """估算页数（按500字/页计算）"""
        if self.word_count > 0:
            return max(1, (self.word_count + 499) // 500)
        return 1

    def update_word_count(self):
        """更新字数统计"""
        if self.prosemirror_content and isinstance(self.prosemirror_content, dict):
            # 这里可以实现更复杂的字数统计逻辑
            # 暂时使用简单的估算
            content_str = str(self.prosemirror_content)
            self.word_count = len(content_str.replace(' ', '').replace('\n', ''))

    def update_status(self, new_status: DocumentStatus, user_id: int):
        """更新文档状态"""
        old_status = self.document_status
        self.document_status = new_status.value
        self.last_edited_by_user_id = user_id
        
        # 记录状态变更到活动日志
        # 这里可以触发活动日志记录
        return old_status

    def create_version(self, user_id: int, change_summary: str = None):
        """创建新版本"""
        new_version = self.document_version + 1
        
        # 这里应该创建 BidDocumentVersion 记录
        version_data = {
            'bid_document_id': self.id,
            'version_number': new_version,
            'prosemirror_content': self.prosemirror_content,
            'change_summary': change_summary,
            'word_count': self.word_count,
            'created_by_user_id': user_id
        }
        
        self.document_version = new_version
        return version_data

    def get_content_summary(self, max_length: int = 200) -> str:
        """获取内容摘要"""
        if self.prosemirror_content and isinstance(self.prosemirror_content, dict):
            # 提取文本内容
            content_text = self._extract_text_from_prosemirror(self.prosemirror_content)
            if len(content_text) > max_length:
                return content_text[:max_length] + "..."
            return content_text
        return ""

    def _extract_text_from_prosemirror(self, content) -> str:
        """从ProseMirror内容中提取纯文本"""
        if isinstance(content, dict):
            if content.get('type') == 'text':
                return content.get('text', '')
            elif 'content' in content:
                text_parts = []
                for item in content['content']:
                    text_parts.append(self._extract_text_from_prosemirror(item))
                return ' '.join(text_parts)
        elif isinstance(content, list):
            text_parts = []
            for item in content:
                text_parts.append(self._extract_text_from_prosemirror(item))
            return ' '.join(text_parts)
        return ""


class BidDocumentVersion(Base):
    """投标书版本历史模型"""
    __tablename__ = "bid_document_versions"

    id = Column(Integer, primary_key=True, index=True)
    bid_document_id = Column(Integer, ForeignKey("bid_documents.id"), nullable=False, index=True)
    version_number = Column(Integer, nullable=False)
    prosemirror_content = Column(JSONB, nullable=False)
    change_summary = Column(Text, nullable=True)
    word_count = Column(Integer, default=0)
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)

    # 唯一约束
    __table_args__ = (
        CheckConstraint(
            'version_number > 0',
            name='check_bid_document_version_number_positive'
        ),
    )

    # 关系定义
    bid_document = relationship("BidDocument", back_populates="versions")
    created_by = relationship("User", foreign_keys=[created_by_user_id])

    def __repr__(self):
        return f"<BidDocumentVersion(id={self.id}, bid_document_id={self.bid_document_id}, version={self.version_number})>"

    def dict(self):
        """转换为字典格式，用于API响应"""
        return {
            "id": self.id,
            "bid_document_id": self.bid_document_id,
            "version_number": self.version_number,
            "prosemirror_content": self.prosemirror_content,
            "change_summary": self.change_summary,
            "word_count": self.word_count,
            "created_by_user_id": self.created_by_user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        } 
#!/usr/bin/env python3
"""
API功能测试脚本
用于验证认证API是否正常工作
"""

import requests
import json
import sys


def test_api():
    """测试API功能"""
    base_url = "http://localhost:8000"
    
    print("🚀 开始测试IntelliBid API...")
    
    # 测试健康检查
    print("\n1. 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ 健康检查通过")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False
    
    # 测试用户注册
    print("\n2. 测试用户注册...")
    register_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "role": "specialist",
        "full_name": "Test User"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/auth/register",
            json=register_data
        )
        if response.status_code == 200:
            print("✅ 用户注册成功")
            user_data = response.json()
            print(f"   用户ID: {user_data['id']}")
            print(f"   用户名: {user_data['username']}")
        else:
            print(f"❌ 用户注册失败: {response.status_code}")
            print(f"   错误: {response.json()}")
            # 如果是用户已存在错误，继续测试
            if response.status_code != 400:
                return False
    except Exception as e:
        print(f"❌ 注册请求失败: {e}")
        return False
    
    # 测试用户登录
    print("\n3. 测试用户登录...")
    login_data = {
        "username": "testuser",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/auth/login/json",
            json=login_data
        )
        if response.status_code == 200:
            print("✅ 用户登录成功")
            token_data = response.json()
            access_token = token_data["access_token"]
            print(f"   令牌类型: {token_data['token_type']}")
            print(f"   过期时间: {token_data['expires_in']}秒")
        else:
            print(f"❌ 用户登录失败: {response.status_code}")
            print(f"   错误: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    # 测试获取当前用户信息
    print("\n4. 测试获取当前用户信息...")
    headers = {"Authorization": f"Bearer {access_token}"}
    
    try:
        response = requests.get(
            f"{base_url}/api/v1/auth/me",
            headers=headers
        )
        if response.status_code == 200:
            print("✅ 获取用户信息成功")
            user_info = response.json()
            print(f"   用户名: {user_info['username']}")
            print(f"   邮箱: {user_info['email']}")
            print(f"   角色: {user_info['role']}")
        else:
            print(f"❌ 获取用户信息失败: {response.status_code}")
            print(f"   错误: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ 获取用户信息请求失败: {e}")
        return False
    
    # 测试用户列表（需要管理员权限，预期失败）
    print("\n5. 测试用户列表（预期权限不足）...")
    try:
        response = requests.get(
            f"{base_url}/api/v1/users/",
            headers=headers
        )
        if response.status_code == 403:
            print("✅ 权限检查正常（specialist用户无法访问用户列表）")
        else:
            print(f"⚠️  权限检查异常: {response.status_code}")
            print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"❌ 用户列表请求失败: {e}")
        return False
    
    print("\n🎉 所有API测试完成！")
    return True


if __name__ == "__main__":
    success = test_api()
    sys.exit(0 if success else 1) 
# 数据库迁移

本目录包含数据库迁移脚本，用于创建和更新数据库表结构。

## 迁移文件

- `001_create_users_table.sql` - 创建用户表和相关索引

## 使用方法

### 开发环境

1. **使用Docker Compose启动数据库**:
   ```bash
   docker-compose up postgres
   ```

2. **手动运行迁移脚本**:
   ```bash
   cd backend
   python scripts/init_db.py
   ```

3. **运行测试验证**:
   ```bash
   cd backend
   pytest tests/test_user_model.py -v
   ```

### 生产环境

生产环境建议使用Alembic进行数据库迁移管理：

```bash
# 初始化Alembic
alembic init alembic

# 生成迁移脚本
alembic revision --autogenerate -m "创建用户表"

# 执行迁移
alembic upgrade head
```

## 数据库连接

默认连接参数：
- 主机: localhost
- 端口: 5432
- 数据库: intellibid
- 用户名: intellibid
- 密码: intellibid123

可通过环境变量 `DATABASE_URL` 覆盖默认配置。

## 用户角色

系统支持三种用户角色：

1. **admin** - 系统管理员
   - 拥有所有权限
   - 可以管理LLM配置
   - 可以管理用户账户

2. **manager** - 项目经理/标书经理
   - 可以创建和管理项目
   - 可以分配任务给专员
   - 可以查看所有项目数据

3. **specialist** - 专员(技术专家/商务专员)
   - 可以查看分配给自己的任务
   - 可以编辑投标书内容
   - 受限的项目访问权限

## 默认管理员账户

系统会自动创建一个默认管理员账户：
- 用户名: admin
- 邮箱: <EMAIL>
- 密码: admin123
- 角色: admin

 
"""
项目活动日志模型
定义项目活动日志表的SQLAlchemy模型
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, CheckConstraint, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB

from ..database import Base


class ActivityType(str, Enum):
    """活动类型枚举"""
    PROJECT_CREATED = "project_created"
    PROJECT_UPDATED = "project_updated"
    PROJECT_STATUS_CHANGED = "project_status_changed"
    TENDER_DOCUMENT_UPLOADED = "tender_document_uploaded"
    TENDER_DOCUMENT_EXTRACTED = "tender_document_extracted"
    ANALYSIS_REPORT_GENERATED = "analysis_report_generated"
    BID_DOCUMENT_CREATED = "bid_document_created"
    BID_DOCUMENT_UPDATED = "bid_document_updated"
    DOCUMENT_EXPORTED = "document_exported"
    MANAGER_ASSIGNED = "manager_assigned"
    PROJECT_ARCHIVED = "project_archived"


class ProjectActivityLog(Base):
    """项目活动日志模型"""
    __tablename__ = "project_activity_logs"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, index=True)
    activity_type = Column(String(50), nullable=False, index=True)
    activity_description = Column(Text, nullable=False)
    activity_data = Column(JSONB, nullable=True)
    performed_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    performed_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)

    # 添加约束
    __table_args__ = (
        CheckConstraint(
            activity_type.in_([
                'project_created', 'project_updated', 'project_status_changed',
                'tender_document_uploaded', 'tender_document_extracted',
                'analysis_report_generated', 'bid_document_created', 
                'bid_document_updated', 'document_exported',
                'manager_assigned', 'project_archived'
            ]),
            name='check_project_activity_type'
        ),
    )

    # 关系定义
    project = relationship("Project", back_populates="activity_logs")
    performed_by = relationship("User", foreign_keys=[performed_by_user_id])

    def __repr__(self):
        return f"<ProjectActivityLog(id={self.id}, type='{self.activity_type}', project_id={self.project_id})>"

    def dict(self):
        """转换为字典格式，用于API响应"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "activity_type": self.activity_type,
            "activity_description": self.activity_description,
            "activity_data": self.activity_data,
            "performed_by_user_id": self.performed_by_user_id,
            "performed_at": self.performed_at.isoformat() if self.performed_at else None,
        }

    def can_be_accessed_by(self, user) -> bool:
        """检查用户是否可以访问此活动日志"""
        from .user import UserRole
        
        # 管理员可以访问所有活动日志
        if user.role == UserRole.ADMIN.value:
            return True
        
        # 通过项目权限检查
        if hasattr(self, 'project') and self.project:
            return self.project.can_be_accessed_by(user)
        
        return False

    def get_formatted_time(self) -> str:
        """获取格式化的时间"""
        if self.performed_at:
            return self.performed_at.strftime("%Y-%m-%d %H:%M:%S")
        return ""

    def get_time_ago(self) -> str:
        """获取相对时间（多久前）"""
        if not self.performed_at:
            return ""
        
        now = datetime.utcnow()
        diff = now - self.performed_at.replace(tzinfo=None)
        
        if diff.days > 0:
            return f"{diff.days}天前"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}小时前"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"

    def get_user_name(self) -> str:
        """获取操作用户名"""
        if hasattr(self, 'performed_by') and self.performed_by:
            return self.performed_by.username
        return "未知用户"

    def get_activity_icon(self) -> str:
        """获取活动图标"""
        icon_map = {
            ActivityType.PROJECT_CREATED.value: "📁",
            ActivityType.PROJECT_UPDATED.value: "✏️",
            ActivityType.PROJECT_STATUS_CHANGED.value: "🔄",
            ActivityType.TENDER_DOCUMENT_UPLOADED.value: "📄",
            ActivityType.TENDER_DOCUMENT_EXTRACTED.value: "🔍",
            ActivityType.ANALYSIS_REPORT_GENERATED.value: "📊",
            ActivityType.BID_DOCUMENT_CREATED.value: "📝",
            ActivityType.BID_DOCUMENT_UPDATED.value: "✍️",
            ActivityType.DOCUMENT_EXPORTED.value: "📤",
            ActivityType.MANAGER_ASSIGNED.value: "👤",
            ActivityType.PROJECT_ARCHIVED.value: "🗃️",
        }
        return icon_map.get(self.activity_type, "📋")

    def get_activity_color(self) -> str:
        """获取活动颜色（用于UI显示）"""
        color_map = {
            ActivityType.PROJECT_CREATED.value: "green",
            ActivityType.PROJECT_UPDATED.value: "blue",
            ActivityType.PROJECT_STATUS_CHANGED.value: "orange",
            ActivityType.TENDER_DOCUMENT_UPLOADED.value: "purple",
            ActivityType.TENDER_DOCUMENT_EXTRACTED.value: "cyan",
            ActivityType.ANALYSIS_REPORT_GENERATED.value: "gold",
            ActivityType.BID_DOCUMENT_CREATED.value: "green",
            ActivityType.BID_DOCUMENT_UPDATED.value: "blue",
            ActivityType.DOCUMENT_EXPORTED.value: "magenta",
            ActivityType.MANAGER_ASSIGNED.value: "volcano",
            ActivityType.PROJECT_ARCHIVED.value: "gray",
        }
        return color_map.get(self.activity_type, "default")

    @classmethod
    def create_log(cls, project_id: int, activity_type: ActivityType, description: str, 
                   user_id: int, activity_data: dict = None):
        """创建活动日志记录"""
        return cls(
            project_id=project_id,
            activity_type=activity_type.value,
            activity_description=description,
            activity_data=activity_data,
            performed_by_user_id=user_id
        )

    @classmethod
    def log_project_created(cls, project_id: int, project_name: str, user_id: int):
        """记录项目创建"""
        return cls.create_log(
            project_id=project_id,
            activity_type=ActivityType.PROJECT_CREATED,
            description=f"创建了项目: {project_name}",
            user_id=user_id,
            activity_data={"project_name": project_name}
        )

    @classmethod
    def log_project_updated(cls, project_id: int, changes: dict, user_id: int):
        """记录项目更新"""
        change_desc = ", ".join([f"{k}: {v}" for k, v in changes.items()])
        return cls.create_log(
            project_id=project_id,
            activity_type=ActivityType.PROJECT_UPDATED,
            description=f"更新了项目信息: {change_desc}",
            user_id=user_id,
            activity_data={"changes": changes}
        )

    @classmethod
    def log_status_changed(cls, project_id: int, old_status: str, new_status: str, user_id: int):
        """记录状态变更"""
        return cls.create_log(
            project_id=project_id,
            activity_type=ActivityType.PROJECT_STATUS_CHANGED,
            description=f"项目状态从 {old_status} 变更为 {new_status}",
            user_id=user_id,
            activity_data={"old_status": old_status, "new_status": new_status}
        )

    @classmethod
    def log_tender_document_uploaded(cls, project_id: int, filename: str, user_id: int):
        """记录招标文档上传"""
        return cls.create_log(
            project_id=project_id,
            activity_type=ActivityType.TENDER_DOCUMENT_UPLOADED,
            description=f"上传了招标文档: {filename}",
            user_id=user_id,
            activity_data={"filename": filename}
        )

    @classmethod
    def log_analysis_report_generated(cls, project_id: int, llm_model: str, user_id: int):
        """记录分析报告生成"""
        return cls.create_log(
            project_id=project_id,
            activity_type=ActivityType.ANALYSIS_REPORT_GENERATED,
            description=f"生成了分析报告 (使用模型: {llm_model})",
            user_id=user_id,
            activity_data={"llm_model": llm_model}
        )

    @classmethod
    def log_bid_document_created(cls, project_id: int, document_title: str, user_id: int):
        """记录投标书创建"""
        return cls.create_log(
            project_id=project_id,
            activity_type=ActivityType.BID_DOCUMENT_CREATED,
            description=f"创建了投标书: {document_title}",
            user_id=user_id,
            activity_data={"document_title": document_title}
        )

    @classmethod
    def log_document_exported(cls, project_id: int, export_format: str, user_id: int):
        """记录文档导出"""
        return cls.create_log(
            project_id=project_id,
            activity_type=ActivityType.DOCUMENT_EXPORTED,
            description=f"导出了文档 (格式: {export_format.upper()})",
            user_id=user_id,
            activity_data={"export_format": export_format}
        )

    @classmethod
    def log_manager_assigned(cls, project_id: int, manager_name: str, user_id: int):
        """记录经理分配"""
        return cls.create_log(
            project_id=project_id,
            activity_type=ActivityType.MANAGER_ASSIGNED,
            description=f"分配了项目经理: {manager_name}",
            user_id=user_id,
            activity_data={"manager_name": manager_name}
        ) 
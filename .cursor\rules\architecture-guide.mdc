---
description: 
globs: 
alwaysApply: true
---
# 系统架构指导原则

## 架构愿景
采用**分层与模块化的服务架构**，由四大核心层次构成：前端应用层、应用服务层、核心引擎层、数据与存储层。

## 设计原则
- **分层与模块化**: 严格遵循关注点分离原则，将系统划分为清晰的层次和独立的模块
- **服务化**: 将核心的、资源密集型功能封装成独立的服务，通过定义良好的API进行交互
- **高可用与可扩展**: 支持水平扩展，确保在用户量和数据量增长时系统性能稳定
- **安全优先**: 将数据安全和访问控制贯穿于架构的每一层
- **可配置与可维护**: 关键业务逻辑必须外部化，允许非开发人员进行迭代优化

## 核心服务层次

### 前端应用层
- **用户工作台**: 面向投标团队的核心操作界面(SPA)
- **管理后台**: 面向系统管理员的独立SPA

### 应用服务层
- **API网关**: 统一的请求入口，负责路由、认证、限流
- **工作流编排服务**: 核心业务逻辑，实现LangChain业务流程
- **用户与权限管理服务**: 管理用户账户、角色和权限
- **项目与文档管理服务**: 项目、文档的CRUD操作及元数据管理

### 核心引擎层
- **文档解析引擎**: 将文档转换为纯文本或结构化文本
- **LLM服务层**: 实现适配器模式，统一LLM调用接口
- **文档导出服务**: 服务端高保真格式转换

### 数据与存储层
- **关系型数据库**: PostgreSQL，存储结构化数据
- **向量数据库**: pgvector，存储知识库向量嵌入
- **对象存储**: MinIO，存储大型二进制文件
- **缓存**: Redis，存储临时会话数据

## 关键技术实现
- **LLM适配器模式**: 为每个LLM提供具体的适配器实现，确保新增模型不影响核心逻辑
- **ProseMirror编辑器**: 自定义Schema，支持结构化文档和AI辅助功能
- **RAG检索**: 基于向量数据库的知识库检索，支持元数据过滤
- **容器化部署**: Docker + Kubernetes，实现服务的弹性伸缩和自动恢复


"""
用户模型
定义用户表的SQLAlchemy模型
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, Boolean, DateTime, CheckConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..database import Base


class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"      # 系统管理员
    MANAGER = "manager"  # 项目经理/标书经理
    SPECIALIST = "specialist"  # 专员(技术专家/商务专员)


class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(255), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False, index=True)
    full_name = Column(String(255))
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 添加角色约束
    __table_args__ = (
        CheckConstraint(
            role.in_(['admin', 'manager', 'specialist']),
            name='check_user_role'
        ),
    )

    # 项目关系 - 使用字符串引用避免循环依赖
    created_projects = relationship("Project", foreign_keys="Project.created_by_user_id", back_populates="created_by")
    managed_projects = relationship("Project", foreign_keys="Project.manager_id", back_populates="manager")

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"

    def dict(self):
        """转换为字典格式，用于API响应"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "role": self.role,
            "full_name": self.full_name,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def has_permission(self, required_role: UserRole) -> bool:
        """检查用户是否具有指定角色权限"""
        role_hierarchy = {
            UserRole.ADMIN: 3,
            UserRole.MANAGER: 2,
            UserRole.SPECIALIST: 1,
        }
        user_level = role_hierarchy.get(UserRole(self.role), 0)
        required_level = role_hierarchy.get(required_role, 0)
        return user_level >= required_level 